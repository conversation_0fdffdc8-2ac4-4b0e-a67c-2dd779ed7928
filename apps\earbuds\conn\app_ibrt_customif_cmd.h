/***************************************************************************
 *
 * Copyright 2015-2021 BES.
 * All rights reserved. All unpublished rights reserved.
 *
 * No part of this work may be used or reproduced in any form or by any
 * means, or stored in a database or retrieval system, without prior written
 * permission of BES.
 *
 * Use of this work is governed by a license granted by BES.
 * This work contains confidential and proprietary information of
 * BES. which is protected by copyright, trade secret,
 * trademark and other intellectual property rights.
 *
 ****************************************************************************/
#ifndef __APP_IBRT_IF_CUSTOM_CMD__
#define __APP_IBRT_IF_CUSTOM_CMD__

#include "app_ibrt_custom_cmd.h"

#define APP_TWS_CMD_PRIO_0       (0x0)
#define APP_TWS_CMD_PRIO_1       (0x1)
#define APP_TWS_CMD_PRIO_2       (0x2)
#define APP_TWS_CMD_PRIO_3       (0x3)
#define APP_TWS_CMD_PRIO_4       (0x4)
#define APP_TWS_CMD_PRIO_5       (0x5)
#define APP_TWS_CMD_PRIO_6       (0x6)
#define APP_TWS_CMD_PRIO_7       (0x7)

#define app_ibrt_custom_cmd_rsp_timeout_handler_null   (0)
#define app_ibrt_custom_cmd_rsp_handler_null           (0)
#define app_ibrt_custom_cmd_rx_handler_null            (0)
#define app_ibrt_custom_cmd_tx_done_handler_null       (0)

typedef enum
{
    APP_IBRT_CUSTOM_CMD_TEST1 = APP_IBRT_CMD_BASE|APP_IBRT_CUSTOM_CMD_PREFIX|0x01,
    APP_IBRT_CUSTOM_CMD_TEST2 = APP_IBRT_CMD_BASE|APP_IBRT_CUSTOM_CMD_PREFIX|0x02,
    //new customer cmd add here
    #ifdef GGEC_MDF_SDK
    #ifdef GGEC_AUTOVOL_ALGO
    APP_IBRT_CUSTOM_CMD_AUTOVOL = APP_IBRT_CMD_BASE|APP_IBRT_CUSTOM_CMD_PREFIX|0x03,
    #endif
    #endif
} app_ibrt_custom_cmd_code_e;

typedef struct
{
    uint8_t rsv;
    uint8_t buff[6];
} __attribute__((packed))ibrt_custom_cmd_test_t;

void app_ibrt_customif_cmd_test(ibrt_custom_cmd_test_t *cmd_test);

#ifdef GGEC_MDF_SDK

#ifdef __cplusplus
extern "C" {
#endif

void ggec_test2_cmd_send(uint8_t *p_buff, uint16_t length);

#ifdef __cplusplus
}
#endif

#endif

#endif
