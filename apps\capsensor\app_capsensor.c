/***************************************************************************
 *
 * Copyright 2022-2023 BES.
 * All rights reserved. All unpublished rights reserved.
 *
 * No part of this work may be used or reproduced in any form or by any
 * means, or stored in a database or retrieval system, without prior written
 * permission of BES.
 *
 * Use of this work is governed by a license granted by BES.
 * This work contains confidential and proprietary information of
 * BES. which is protected by copyright, trade secret,
 * trademark and other intellectual property rights.
 *
 ****************************************************************************/
#ifndef CHIP_SUBSYS_SENS
#include "app_capsensor.h"
#include "stdint.h"
#include "touch_wear_core.h"
#include "plat_addr_map.h"
#include CHIP_SPECIFIC_HDR(capsensor_driver)
#include "capsensor_driver.h"
#if defined(CAPSENSOR_AT_SENS)
#include "app_sensor_hub.h"
#endif
#include "hal_trace.h"
#include "app_key.h"
#include "stdint.h"
#include "string.h"

#ifdef GGEC_MDF_SDK
#include "ggec_event_if.h"
#include "bes_bt_api.h"
#include "app_media_player.h"
#include "bt_evt_handle.h"
extern void ggec_out_in_ear_evt(uint32_t evt);

#ifdef GGEC_HISENSE_ALGO
extern void drop_algo_switch(bool drop_switch_flag);//开启海信跌落算法执行5s后自动关闭
#endif

#endif

void app_capsensor_click_event(uint8_t key_event);

#if defined(CAPSENSOR_AT_SENS)
static void app_mcu_sensor_hub_transmit_touch_no_rsp_cmd_handler(uint8_t* ptr, uint16_t len)
{
    app_core_bridge_send_data_without_waiting_rsp(MCU_SENSOR_HUB_TASK_CMD_TOUCH_REQ_NO_RSP, ptr, len);
}

static void app_mcu_sensor_hub_touch_no_rsp_cmd_received_handler(uint8_t* ptr, uint16_t len)
{
    APP_KEY_STATUS status = *((APP_KEY_STATUS *)ptr);
    CAPSENSOR_TRACE(0, "Get touch no rsp command from sensor hub core, status.event: %d", status.event);
    app_capsensor_click_event(status.event);
}

static void app_mcu_sensor_hub_touch_no_rsp_cmd_tx_done_handler(uint16_t cmdCode,
    uint8_t* ptr, uint16_t len)
{
    CAPSENSOR_TRACE(0, "cmdCode 0x%x tx done", cmdCode);
}

CORE_BRIDGE_TASK_COMMAND_TO_ADD(MCU_SENSOR_HUB_TASK_CMD_TOUCH_REQ_NO_RSP,
                                "touch no rsp req to sensor hub core",
                                app_mcu_sensor_hub_transmit_touch_no_rsp_cmd_handler,
                                app_mcu_sensor_hub_touch_no_rsp_cmd_received_handler,
                                0,
                                NULL,
                                NULL,
                                app_mcu_sensor_hub_touch_no_rsp_cmd_tx_done_handler);
#endif

/***************************************************************************
 * @brief app_capsensor_click_event function
 *
 * @param key_event touch or wear event
 ***************************************************************************/
#ifdef GGEC_MDF_SDK
extern uint8_t g_out_dock_cnt;
#endif
void app_capsensor_click_event(uint8_t key_event)
{
    APP_KEY_STATUS status;

    status.event = key_event;
    CAPSENSOR_TRACE(1, "[%s]:%d",  __func__, status.event);

    switch (status.event)
    {
        case CAP_KEY_EVENT_OFF_EAR:
            CAPSENSOR_TRACE(1, "capsensor state= off ear\n");
            status.event = APP_KEY_EVENT_OFF_EAR;
            #ifdef GGEC_MDF_SDK
            if (g_out_dock_cnt < 2) {
                CAPSENSOR_TRACE(1, "g_out_dock_cnt=%d \n",g_out_dock_cnt);
                break;
            }
            GetMqApi()->SendTopic(KEY_TOPIC, CAP_KEY_OFF_EAR_EVT, NULL, 0);            
            #ifdef GGEC_HISENSE_ALGO
            drop_algo_switch(0);//出耳5s后关闭海信掉落算法
            #endif

            #ifdef GGEC_CWM_ALGO
            ggec_out_in_ear_evt(CAP_KEY_OFF_EAR_EVT);
            osDelay(300);
            #endif
            #endif
            break;

        case CAP_KEY_EVENT_ON_EAR:
            CAPSENSOR_TRACE(1, "capsensor state= on ear\n");
            status.event = APP_KEY_EVENT_ON_EAR;
            #ifdef GGEC_MDF_SDK
            ggec_set_find_earbud_status(false, false);
            GetMqApi()->SendTopic(KEY_TOPIC, VAD_EVT_CHECK_PHONE, NULL, 0);
            media_PlayAudio_locally(AUD_ID_HS01_CLICK, 0);
            if (g_out_dock_cnt < 2) {
                CAPSENSOR_TRACE(1, "g_out_dock_cnt=%d \n",g_out_dock_cnt);
                break;
            }
            GetMqApi()->SendTopic(KEY_TOPIC, CAP_KEY_ON_EAR_EVT, NULL, 0);            
            #ifdef GGEC_HISENSE_ALGO
            drop_algo_switch(1);//入耳开启海信掉落算法
            #endif
            
            #ifdef GGEC_CWM_ALGO
            osDelay(500); // Delay to ensure the earbud is out of the ear before sending the event
            ggec_out_in_ear_evt(CAP_KEY_ON_EAR_EVT);
            #endif
            #endif
            break;

        case CAP_KEY_EVENT_UP:
            CAPSENSOR_TRACE(1, "capsensor state= key up\n");
            status.event = APP_KEY_EVENT_UP;
            break;

        case CAP_KEY_EVENT_DOWN:
            CAPSENSOR_TRACE(1, "capsensor state= key down\n");
            status.event = APP_KEY_EVENT_DOWN;
            break;

        case CAP_KEY_EVENT_UPSLIDE:
            CAPSENSOR_TRACE(1, "capsensor state= up slide\n");
            status.event = APP_KEY_EVENT_UPSLIDE;
            break;

        case CAP_KEY_EVENT_DOWNSLIDE:
            CAPSENSOR_TRACE(1, "capsensor state= down slide\n");
            status.event = APP_KEY_EVENT_DOWNSLIDE;
            break;

        case CAP_KEY_EVENT_CLICK:
            CAPSENSOR_TRACE(1, "capsensor state= single click\n");
            status.event = APP_KEY_EVENT_CLICK;
            break;

        case CAP_KEY_EVENT_DOUBLECLICK:
            CAPSENSOR_TRACE(1, "capsensor state= double click\n");
            status.event = APP_KEY_EVENT_DOUBLECLICK;
            break;

        case CAP_KEY_EVENT_TRIPLECLICK:
            CAPSENSOR_TRACE(1, "capsensor state= triple click\n");
            status.event = APP_KEY_EVENT_TRIPLECLICK;
            break;

        case CAP_KEY_EVENT_ULTRACLICK:
            CAPSENSOR_TRACE(1, "capsensor state= ultra click\n");
            status.event = APP_KEY_EVENT_ULTRACLICK;
            break;

        case CAP_KEY_EVENT_RAMPAGECLICK:
            CAPSENSOR_TRACE(1, "capsensor state= rampage click\n");
            status.event = APP_KEY_EVENT_RAMPAGECLICK;
            break;

        case CAP_KEY_EVENT_SIXCLICK:
            CAPSENSOR_TRACE(1, "capsensor state= six click\n");
            break;

        case CAP_KEY_EVENT_SEVENCLICK:
            CAPSENSOR_TRACE(1, "capsensor state= seven click\n");
            break;

        case CAP_KEY_EVENT_LONGPRESS:
            CAPSENSOR_TRACE(1, "capsensor state= long press\n");
            status.event = APP_KEY_EVENT_LONGPRESS;
            break;

        case CAP_KEY_EVENT_LONGLONGPRESS:
            CAPSENSOR_TRACE(1, "capsensor state= long  long press\n");
            status.event = APP_KEY_EVENT_LONGLONGPRESS;
            break;

        case CAP_KEY_EVENT_CLICK_AND_LONGPRESS:
            CAPSENSOR_TRACE(1, "capsensor state= click and long press\n");
            break;

        case CAP_KEY_EVENT_CLICK_AND_LONGLONGPRESS:
            CAPSENSOR_TRACE(1, "capsensor state= click and longlongpress\n");
            break;

        case CAP_KEY_EVENT_DOUBLECLICK_AND_LONGLONGPRESS:
            CAPSENSOR_TRACE(1, "capsensor state= double click and longlongpress\n");
            break;

        case CAP_KEY_EVENT_TRIPLECLICK_AND_LONGLONGPRESS:
            CAPSENSOR_TRACE(1, "capsensor state= triple click and longlongpress\n");
            break;

        default:
            break;
    }
}

/***************************************************************************
 * @brief  app_mcu_core_capsensor_init function
 *
 ***************************************************************************/
void app_mcu_core_capsensor_init(void)
{
    int ret = -1;
    ret = register_capsensor_click_event_callback(app_capsensor_click_event);
    if (ret) {
        CAPSENSOR_TRACE(1,"register_capsensor_click_event_callback failed:%d\n", ret);
    }

    capsensor_driver_init();

#ifdef CAPSENSOR_AT_MCU
    capsensor_sens2mcu_irq_set();
    cap_sensor_core_thread_init();
#endif
}

#endif

