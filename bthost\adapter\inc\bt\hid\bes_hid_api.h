/***************************************************************************
 *
 * Copyright 2015-2023 BES.
 * All rights reserved. All unpublished rights reserved.
 *
 * No part of this work may be used or reproduced in any form or by any
 * means, or stored in a database or retrieval system, without prior written
 * permission of BES.
 *
 * Use of this work is governed by a license granted by BES.
 * This work contains confidential and proprietary information of
 * BES. which is protected by copyright, trade secret,
 * trademark and other intellectual property rights.
 *
 ****************************************************************************/
#ifndef __BES_HID_API_H__
#define __BES_HID_API_H__

#ifdef __cplusplus
extern "C" {
#endif

void bes_hid_device_send_consumer_ctrl_request(uint8_t ctrl_key);

#ifdef __cplusplus
}
#endif
#endif /* __BES_HID_API_H__ */
