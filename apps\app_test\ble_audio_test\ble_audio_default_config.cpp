/***************************************************************************
 *
 * Copyright 2015-2022 BES.
 * All rights reserved. All unpublished rights reserved.
 *
 * No part of this work may be used or reproduced in any form or by any
 * means, or stored in a database or retrieval system, without prior written
 * permission of BES.
 *
 * Use of this work is governed by a license granted by BES.
 * This work contains confidential and proprietary information of
 * BES. which is protected by copyright, trade secret,
 * trademark and other intellectual property rights.
 *
 ****************************************************************************/
#if (BLE_AUDIO_ENABLED)
#include "cmsis.h"
#include "cmsis_os.h"
#include "hal_trace.h"
#include "hal_aud.h"
#include "app_trace_rx.h"
#include "plat_types.h"
#include "ble_audio_dbg.h"
#include "heap_api.h"
#include "tgt_hardware.h"

#include "app_gaf_custom_api.h"
#include "app_ibrt_internal.h"
#include "aob_gaf_api.h"
#include "ble_audio_test.h"
#include "aob_conn_api.h"
#include "ble_audio_core_api.h"
#include "ble_audio_earphone_info.h"
#include "gaf_media_stream.h"
#ifdef GAF_DECODER_CROSS_CORE_USE_M55
#include "gaf_codec_cc_common.h"
#endif

/************************private macro defination***************************/

/************************private type defination****************************/
typedef struct {
    unsigned char address[BLE_ADDR_SIZE];
} __attribute__ ((packed)) ble_address_t;

typedef struct
{
    ble_address_t mobile_bleaddr;
    ble_address_t master_bleaddr;
    ble_address_t slave_bleaddr;
} tws_pairing_info_t;

/************************private variable defination************************/
static const tws_pairing_info_t g_tws_pairing_info[] =
{
    {{0x82, 0x33, 0x33, 0x23, 0x22, 0x11},{0x81, 0x33, 0x33, 0x23, 0x22, 0x11},{0x80, 0x33, 0x33, 0x23, 0x22, 0x11}}, // shawn
    {{0x82, 0x33, 0x01, 0x12, 0x85, 0x19},{0x81, 0x33, 0x01, 0x12, 0x85, 0x19},{0x80, 0x33, 0x01, 0x12, 0x85, 0x19}}, // shawn2

    {{0x87, 0x33, 0x33, 0x23, 0x22, 0x11},{0x86, 0x33, 0x33, 0x23, 0x22, 0x11},{0x85, 0x33, 0x33, 0x23, 0x22, 0x11}}, // zhaochunyu00
    {{0x52, 0x55, 0x33, 0x23, 0x22, 0x11},{0x51, 0x55, 0x33, 0x23, 0x22, 0x11},{0x50, 0x55, 0x33, 0x23, 0x22, 0x11}}, //wp
    {{0x52, 0x33, 0x33, 0x23, 0x22, 0x11},{0x51, 0x33, 0x33, 0x23, 0x22, 0x11},{0x50, 0x33, 0x33, 0x23, 0x22, 0x11}},
    {{0x57, 0x33, 0x33, 0x23, 0x22, 0x11},{0x56, 0x33, 0x33, 0x23, 0x22, 0x11},{0x55, 0x33, 0x33, 0x23, 0x22, 0x11}},
    {{0x62, 0x33, 0x33, 0x23, 0x22, 0x11},{0x61, 0x33, 0x33, 0x23, 0x22, 0x11},{0x60, 0x33, 0x33, 0x23, 0x22, 0x11}}, // Brian
    {{0x6c, 0x33, 0x33, 0x23, 0x22, 0x11},{0x6b, 0x33, 0x33, 0x23, 0x22, 0x11},{0x6a, 0x33, 0x33, 0x23, 0x22, 0x11}}, // freddie
    {{0x92, 0x33, 0x33, 0x23, 0x22, 0x11},{0x91, 0x33, 0x33, 0x23, 0x22, 0x11},{0x90, 0x33, 0x33, 0x23, 0x22, 0x11}}, //ywy
    {{0x8a, 0x33, 0x33, 0x23, 0x22, 0x11},{0x82, 0x35, 0x68, 0x24, 0x19, 0x17},{0x81, 0x35, 0x68, 0x24, 0x19, 0x17}}, // rhy

    {{0x62, 0x66, 0x66, 0x66, 0x66, 0x82},{0x16, 0x66, 0x66, 0x66, 0x66, 0x18},{0x61, 0x66, 0x66, 0x66, 0x66, 0x81}}, // zyx, dual mode
    {{0x64, 0x66, 0x66, 0x66, 0x66, 0x84},{0x36, 0x66, 0x66, 0x66, 0x66, 0x38},{0x63, 0x66, 0x66, 0x66, 0x66, 0x83}}, // dolphin test 1
    {{0x66, 0x66, 0x66, 0x66, 0x66, 0x86},{0x56, 0x66, 0x66, 0x66, 0x66, 0x58},{0x65, 0x66, 0x66, 0x66, 0x66, 0x85}}, // dolphin test 2
    {{0xe4, 0x63, 0x96, 0x52, 0x81, 0x41},{0xe3, 0x63, 0x96, 0x52, 0x81, 0x41},{0xe2, 0x63, 0x96, 0x52, 0x81, 0x41}}, // shhx
    {{0x52, 0x33, 0x33, 0x22, 0x11, 0x11},{0x51, 0x33, 0x33, 0x22, 0x11, 0x11},{0x50, 0x33, 0x33, 0x22, 0x11, 0x11}}, // lyh
    {{0x89, 0xaa, 0x33, 0x22, 0x11, 0x11},{0x88, 0xaa, 0x33, 0x22, 0x11, 0x11},{0x87, 0xaa, 0x33, 0x22, 0x11, 0x11}}, // xrr
    {{0x72, 0x33, 0x33, 0x23, 0x22, 0x11},{0x71, 0x33, 0x33, 0x23, 0x22, 0x11},{0x70, 0x33, 0x33, 0x23, 0x22, 0x11}}, // test1
    {{0x82, 0x33, 0x33, 0x23, 0x22, 0x11},{0x81, 0x33, 0x33, 0x23, 0x22, 0x11},{0x80, 0x33, 0x33, 0x23, 0x22, 0x11}}, // test2
    {{0x92, 0x33, 0x33, 0x23, 0x22, 0x11},{0x91, 0x33, 0x33, 0x23, 0x22, 0x11},{0x90, 0x33, 0x33, 0x23, 0x22, 0x11}}, // test3
    {{0x98, 0x44, 0x33, 0x23, 0x22, 0x11},{0x64, 0x66, 0x66, 0x66, 0x66, 0x84},{0x46, 0x66, 0x66, 0x66, 0x66, 0x48}}, // test4
    {{0x62, 0x44, 0x33, 0x23, 0x22, 0x11},{0x65, 0x66, 0x66, 0x66, 0x66, 0x85},{0x56, 0x66, 0x66, 0x66, 0x66, 0x58}}, // test5
    {{0x66, 0x44, 0x33, 0x23, 0x11, 0x11},{0xaa, 0x66, 0x66, 0x66, 0x66, 0x86},{0xaa, 0x66, 0x66, 0x66, 0x66, 0x68}}, // test6
    {{0x6a, 0x44, 0x33, 0x23, 0x22, 0x11},{0x67, 0x66, 0x66, 0x66, 0x66, 0x87},{0x76, 0x66, 0x66, 0x66, 0x66, 0x78}}, // test7

    // bis test address
    {{0x67, 0x33, 0x33, 0x23, 0x22, 0x11},{0x66, 0x33, 0x33, 0x23, 0x22, 0x11},{0x65, 0x33, 0x33, 0x23, 0x22, 0x11}},
    {{0x77, 0x33, 0x33, 0x23, 0x22, 0x11},{0x76, 0x33, 0x33, 0x23, 0x22, 0x11},{0x75, 0x33, 0x33, 0x23, 0x22, 0x11}}, //LSK

    {{0x97, 0x33, 0x33, 0x23, 0x22, 0x11},{0x96, 0x33, 0x33, 0x23, 0x22, 0x11},{0x95, 0x33, 0x33, 0x23, 0x22, 0x11}}, //zyy
    {{0xA7, 0x33, 0x33, 0x23, 0x22, 0x11},{0xA6, 0x33, 0x33, 0x23, 0x22, 0x11},{0xA5, 0x33, 0x33, 0x23, 0x22, 0x11}}, //wzy

    {{0x53, 0x66, 0x66, 0x66, 0x66, 0x73},{0x25, 0x66, 0x66, 0x66, 0x66, 0x27},{0x52, 0x66, 0x66, 0x66, 0x66, 0x72}}, //customer01
    {{0x40, 0x33, 0x33, 0x23, 0x22, 0x11},{0x39, 0x33, 0x33, 0x23, 0x22, 0x11},{0x38, 0x33, 0x33, 0x23, 0x22, 0x11}}, //customer02
    {{0x45, 0x33, 0x33, 0x23, 0x22, 0x11},{0x44, 0x33, 0x33, 0x23, 0x22, 0x11},{0x43, 0x33, 0x33, 0x23, 0x22, 0x11}}, //customer03
    {{0x88, 0x33, 0x33, 0x23, 0x22, 0x11},{0x87, 0x33, 0x33, 0x23, 0x22, 0x11},{0x86, 0x33, 0x33, 0x23, 0x22, 0x11}}, //customer04
    {{0x91, 0x33, 0x33, 0x23, 0x22, 0x11},{0x90, 0x33, 0x33, 0x23, 0x22, 0x11},{0x89, 0x33, 0x33, 0x23, 0x22, 0x11}}, //customer05
    {{0x40, 0x31, 0x31, 0x23, 0x22, 0x11},{0x39, 0x31, 0x31, 0x23, 0x22, 0x11},{0x37, 0x31, 0x31, 0x23, 0x22, 0x11}}, //customer06
    {{0x45, 0x31, 0x31, 0x23, 0x22, 0x11},{0x44, 0x31, 0x31, 0x23, 0x22, 0x11},{0x42, 0x31, 0x31, 0x23, 0x22, 0x11}}, //customer07
    {{0x49, 0x31, 0x31, 0x23, 0x22, 0x11},{0x48, 0x31, 0x31, 0x23, 0x22, 0x11},{0x47, 0x31, 0x31, 0x23, 0x22, 0x11}}, //customer08
    {{0x53, 0x31, 0x31, 0x23, 0x22, 0x11},{0x52, 0x31, 0x31, 0x23, 0x22, 0x11},{0x51, 0x31, 0x31, 0x23, 0x22, 0x11}}, //customer09
    {{0x57, 0x31, 0x31, 0x23, 0x22, 0x11},{0x56, 0x31, 0x31, 0x23, 0x22, 0x11},{0x55, 0x31, 0x31, 0x23, 0x22, 0x11}}, //customer10

    {{0x61, 0x31, 0x31, 0x23, 0x22, 0x11},{0x60, 0x31, 0x31, 0x23, 0x22, 0x11},{0x59, 0x31, 0x31, 0x23, 0x22, 0x11}}, //BES_01
    {{0x65, 0x31, 0x31, 0x23, 0x22, 0x11},{0x64, 0x31, 0x31, 0x23, 0x22, 0x11},{0x63, 0x31, 0x31, 0x23, 0x22, 0x11}}, //BES_02
    {{0x69, 0x31, 0x31, 0x23, 0x22, 0x11},{0x68, 0x31, 0x31, 0x23, 0x22, 0x11},{0x67, 0x31, 0x31, 0x23, 0x22, 0x11}}, //BES_03
    {{0x73, 0x31, 0x31, 0x23, 0x22, 0x11},{0x72, 0x31, 0x31, 0x23, 0x22, 0x11},{0x71, 0x31, 0x31, 0x23, 0x22, 0x11}}, //BES_04
    {{0x77, 0x31, 0x31, 0x23, 0x22, 0x11},{0x76, 0x31, 0x31, 0x23, 0x22, 0x11},{0x75, 0x31, 0x31, 0x23, 0x22, 0x11}}, //BES_05
    {{0x81, 0x31, 0x31, 0x23, 0x22, 0x11},{0x80, 0x31, 0x31, 0x23, 0x22, 0x11},{0x79, 0x31, 0x31, 0x23, 0x22, 0x11}}, //BES_06
    {{0x23, 0x66, 0x66, 0x66, 0x66, 0x43},{0x22, 0x66, 0x66, 0x66, 0x66, 0x24},{0x22, 0x66, 0x66, 0x66, 0x66, 0x42}}, //BES_07
    {{0x43, 0x66, 0x66, 0x66, 0x66, 0x63},{0x24, 0x66, 0x66, 0x66, 0x66, 0x26},{0x42, 0x66, 0x66, 0x66, 0x66, 0x62}}, //BES_08
    {{0x83, 0x66, 0x66, 0x66, 0x66, 0x04},{0x28, 0x66, 0x66, 0x66, 0x66, 0x30},{0x82, 0x66, 0x66, 0x66, 0x66, 0x03}}, //BES_09
    {{0x04, 0x66, 0x66, 0x66, 0x66, 0x24},{0x30, 0x66, 0x66, 0x66, 0x66, 0x32},{0x03, 0x66, 0x66, 0x66, 0x66, 0x23}}, //BES_10
    {{0x84, 0x66, 0x66, 0x66, 0x66, 0x05},{0x38, 0x66, 0x66, 0x66, 0x66, 0x40},{0x83, 0x66, 0x66, 0x66, 0x66, 0x04}}, //BES_11

    {{0xcc, 0x9a, 0x78, 0x56, 0x34, 0x12},{0xbb, 0x9a, 0x78, 0x56, 0x34, 0x12},{0xaa, 0x9a, 0x78, 0x56, 0x34, 0x12}}, //hejunxiang
    {{0x62, 0x33, 0x33, 0x22, 0x11, 0x11},{0x61, 0x33, 0x33, 0x22, 0x11, 0x11},{0x60, 0x33, 0x33, 0x22, 0x11, 0x11}}, //auto test1
    {{0x72, 0x33, 0x33, 0x22, 0x11, 0x11},{0x71, 0x33, 0x33, 0x22, 0x11, 0x11},{0x70, 0x33, 0x33, 0x22, 0x11, 0x11}}, //auto test2
    {{0x82, 0x33, 0x33, 0x22, 0x11, 0x11},{0x81, 0x33, 0x33, 0x22, 0x11, 0x11},{0x80, 0x33, 0x33, 0x22, 0x11, 0x11}}, //auto test3
    {{0x92, 0x33, 0x33, 0x22, 0x11, 0x11},{0x91, 0x33, 0x33, 0x22, 0x11, 0x11},{0x90, 0x33, 0x33, 0x22, 0x11, 0x11}}, //auto test4
    {{0x54, 0x33, 0x33, 0x22, 0x11, 0x11},{0x53, 0x33, 0x33, 0x22, 0x11, 0x11},{0x52, 0x33, 0x33, 0x22, 0x11, 0x11}}, //auto test5

    {{0x82, 0xda, 0x61, 0xbb, 0xc6, 0x5c}, {0x81, 0xda, 0x61, 0xbb, 0xc6, 0x5c}, {0x80, 0xda, 0x61, 0xbb, 0xc6, 0x5c}}, //Aragon
    {{0x57, 0xda, 0x61, 0xe9, 0xc6, 0x5c}, {0x56, 0xda, 0x61, 0xe9, 0xc6, 0x5c}, {0x55, 0xda, 0x61, 0xe9, 0xc6, 0x5c}}, //
    {{0x89, 0x33, 0x33, 0x2f, 0x22, 0x11}, {0x90, 0x33, 0x33, 0x2f, 0x22, 0x11}, {0x91, 0x33, 0x33, 0x2f, 0x22, 0x11}}, /*dual mode,test 4*/
};

/****************************function defination****************************/
const unsigned char* ble_audio_test_read_peer_tws_addr(void)
{
    const tws_pairing_info_t *tws_pairing_info_lst = g_tws_pairing_info;
    uint32_t lst_size = sizeof(g_tws_pairing_info)/sizeof(tws_pairing_info_t);

    APP_TEST_TRACE(0, "%s", __func__);
    APP_TEST_DUMP8("%x ", ble_global_addr, BLE_ADDR_SIZE);

    for(uint32_t info_index = 0; info_index < lst_size; info_index++)
    {
        if (!memcmp(tws_pairing_info_lst[info_index].master_bleaddr.address, ble_global_addr, BLE_ADDR_SIZE))
        {
            return tws_pairing_info_lst[info_index].slave_bleaddr.address;
        }
        else if (!memcmp(tws_pairing_info_lst[info_index].slave_bleaddr.address, ble_global_addr, BLE_ADDR_SIZE))
        {

            return tws_pairing_info_lst[info_index].master_bleaddr.address;
        }
    }

    return NULL;
}

void ble_audio_test_update_role(void)
{
    const tws_pairing_info_t *tws_pairing_info_lst = g_tws_pairing_info;
    uint32_t lst_size = sizeof(g_tws_pairing_info)/sizeof(tws_pairing_info_t);
    uint32_t info_index = 0;

    for(info_index = 0; info_index < lst_size; info_index++)
    {
        if (!memcmp(tws_pairing_info_lst[info_index].master_bleaddr.address, ble_global_addr, BLE_ADDR_SIZE))
        {
            ble_audio_set_tws_nv_role(BLE_AUDIO_TWS_MASTER);
            ble_audio_update_tws_current_role(BLE_AUDIO_TWS_MASTER);
            break;
        }
        else if (!memcmp(tws_pairing_info_lst[info_index].slave_bleaddr.address, ble_global_addr, BLE_ADDR_SIZE))
        {
            ble_audio_set_tws_nv_role(BLE_AUDIO_TWS_SLAVE);
            ble_audio_update_tws_current_role(BLE_AUDIO_TWS_SLAVE);
            break;
        }
        else if (!memcmp(tws_pairing_info_lst[info_index].mobile_bleaddr.address, ble_global_addr, BLE_ADDR_SIZE))
        {
            ble_audio_set_tws_nv_role(BLE_AUDIO_MOBILE);
            ble_audio_update_tws_current_role(BLE_AUDIO_MOBILE);
            break;
        }
    }
}

void ble_audio_test_config(void)
{
    const tws_pairing_info_t *tws_pairing_info_lst = g_tws_pairing_info;
    uint32_t lst_size = sizeof(g_tws_pairing_info)/sizeof(tws_pairing_info_t);
    uint32_t info_index = 0;

    APP_TEST_TRACE(0, "%s", __func__);
    APP_TEST_DUMP8("%x ", ble_global_addr, BLE_ADDR_SIZE);

    for(info_index = 0; info_index < lst_size; info_index++)
    {
        if (!memcmp(tws_pairing_info_lst[info_index].master_bleaddr.address, ble_global_addr, BLE_ADDR_SIZE))
        {
            ble_audio_set_tws_nv_role(BLE_AUDIO_TWS_MASTER);
            ble_audio_update_tws_current_role(BLE_AUDIO_TWS_MASTER);
            ble_audio_set_tws_local_ble_addr(ble_global_addr);
            ble_audio_set_tws_peer_ble_addr(tws_pairing_info_lst[info_index].slave_bleaddr.address);
            ble_audio_set_bis_src_ble_addr(tws_pairing_info_lst[info_index].mobile_bleaddr.address);
            break;
        }
        else if (!memcmp(tws_pairing_info_lst[info_index].slave_bleaddr.address, ble_global_addr, BLE_ADDR_SIZE))
        {
            ble_audio_set_tws_nv_role(BLE_AUDIO_TWS_SLAVE);
            ble_audio_update_tws_current_role(BLE_AUDIO_TWS_SLAVE);
            ble_audio_set_tws_local_ble_addr(ble_global_addr);
            ble_audio_set_tws_peer_ble_addr(tws_pairing_info_lst[info_index].master_bleaddr.address);
            ble_audio_set_bis_src_ble_addr(tws_pairing_info_lst[info_index].mobile_bleaddr.address);
            break;
        }
        else if (!memcmp(tws_pairing_info_lst[info_index].mobile_bleaddr.address, ble_global_addr, BLE_ADDR_SIZE))
        {
            ble_audio_set_tws_nv_role(BLE_AUDIO_MOBILE);
            ble_audio_update_tws_current_role(BLE_AUDIO_MOBILE);
            ble_audio_set_bis_src_ble_addr(tws_pairing_info_lst[info_index].mobile_bleaddr.address);
            break;
        }
    }

#ifdef BLE_AUDIO_TEST_ENABLED
    if (info_index >= lst_size)
    {
        APP_TEST_TRACE(0,"!!!For uart cmd ble audio test purpose, ble address should be from g_tws_pairing_info!!!!");
        return;
    }
#endif
    if (ble_audio_is_ux_master() || ble_audio_is_ux_slave())
    {
        ble_audio_tws_init();
    }
    else if (ble_audio_is_ux_mobile())
    {
        APP_TEST_TRACE(0,"mobile role");
#ifdef AOB_MOBILE_ENABLED
        aob_gaf_mobile_init();
#ifdef GAF_DECODER_CROSS_CORE_USE_M55
        if (ble_audio_is_ux_mobile())
        {
            is_support_ble_audio_mobile_m55_decode = true;
        }
#endif
#endif

        bes_ble_gap_force_switch_adv(BLE_SWITCH_USER_FPGA, false);
    }
    aob_gaf_bis_init();
}

void ble_audio_test_config_dynamic_audio_sharing_master(void)
{
    APP_TEST_TRACE(0, "%s", __func__);
    APP_TEST_DUMP8("%x ", ble_global_addr, BLE_ADDR_SIZE);

    if (ble_audio_is_ux_master())
    {
        ble_audio_tws_init();
    }
}

bool ble_audio_test_check_device_is_master(uint8_t *address)
{
#ifdef DYNAMIC_MASTER_AUDIO_SHARING
#ifdef AOB_MOBILE_ENABLED     //This part is only for verification of Master Addr when using dynamic master audio-sharing
    if (!memcmp(ble_audio_get_tws_peer_ble_addr(), address, BLE_ADDR_SIZE))
    {
        return true;
    }
#endif
#endif
    const tws_pairing_info_t *tws_pairing_info_lst = g_tws_pairing_info;
    uint32_t lst_size = sizeof(g_tws_pairing_info)/sizeof(tws_pairing_info_t);
    uint32_t info_index = 0;

    for (info_index = 0; info_index < lst_size; info_index++)
    {
        if (!memcmp(tws_pairing_info_lst[info_index].mobile_bleaddr.address, ble_global_addr, BLE_ADDR_SIZE))
        {
            if (!memcmp(tws_pairing_info_lst[info_index].master_bleaddr.address, address, BLE_ADDR_SIZE))
            {
                return true;
            }
        }
    }

    return false;
}

#endif