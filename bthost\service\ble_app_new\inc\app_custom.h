/***************************************************************************
 *
 * Copyright 2015-2023 BES.
 * All rights reserved. All unpublished rights reserved.
 *
 * No part of this work may be used or reproduced in any form or by any
 * means, or stored in a database or retrieval system, without prior written
 * permission of BES.
 *
 * Use of this work is governed by a license granted by BES.
 * This work contains confidential and proprietary information of
 * BES. which is protected by copyright, trade secret,
 * trademark and other intellectual property rights.
 *
 ****************************************************************************/
#ifndef __APP_CUSTOMIF_H__
#define __APP_CUSTOMIF_H__
#include "ble_core_common.h"
#include "hal_timer.h"
#ifdef __cplusplus
extern "C" {
#endif

///for adv scan add call_back user
typedef enum
{
    BLE_SCAN_RESULT_USER_ID_0,
    BLE_SCAN_RESULT_USER_ID_1,

    BLE_SCAN_RESULT_USER_MAX
}BLE_SCAN_RESULT_USER_ID_T;

enum custom_resol_fill_user
{
    APP_CUSTOM_RES_LIST_FILL_USER_0,
    APP_CUSTOM_RES_LIST_FILL_USER_MAX,
};

typedef void (*ble_adv_data_report_cb_t)(ble_bdaddr_t *bleAddr, int8_t rssi, uint8_t evt_type, uint8_t *adv_buf, uint8_t len);
typedef void (*ble_link_event_report_cb_t)(ble_event_handled_t event_param, ble_evnet_type_e event_type);
typedef void (*ble_resolving_list_fill_cb_t)(ble_bdaddr_t *bleAddr, uint8_t *peer_irk, uint8_t *local_irk);

void app_ble_customif_init(void);
void app_ble_customif_adv_report_callback_register(BLE_SCAN_RESULT_USER_ID_T id, ble_adv_data_report_cb_t cb);
void app_ble_customif_adv_report_callback_get(BLE_SCAN_RESULT_USER_ID_T id, ble_adv_data_report_cb_t *p_cb);
void app_ble_customif_adv_report_callback_deregister(BLE_SCAN_RESULT_USER_ID_T id);
void app_ble_customif_link_event_callback_register(ble_link_event_report_cb_t cb);
void app_ble_customif_link_event_callback_deregister(void);
void app_ble_customif_resol_list_fill_cb_register(uint8_t user, ble_resolving_list_fill_cb_t cb);

typedef struct
{
    BLE_ADV_ACTIVITY_USER_E adv_actv_user;
    bool user_enable;
    BLE_ADV_USER_E adv_user;
    bool withFlags;
    bool withName;
    uint8_t filter_pol;
    int8_t tx_power_dbm;
    BLE_ADV_TYPE_E advType;
    ADV_MODE_E advMode;
    uint32_t advUserInterval;
    uint32_t PeriodicIntervalMin;
    uint32_t PeriodicIntervalMax;
    gap_dt_buf_t adv_data;
    gap_dt_buf_t scan_rsp_data;
    uint8_t localAddrType;
    bt_bdaddr_t localAddr;
    ble_bdaddr_t peerAddr;
    uint16_t duration;
    uint8_t max_adv_evts;
} CUSTOMER_ADV_PARAM_T;

CUSTOMER_ADV_PARAM_T * app_ble_custom_adv_param_ptr(BLE_ADV_ACTIVITY_USER_E actv_user);

void app_ble_custom_init(void);
void app_ble_custom_adv_start(BLE_ADV_ACTIVITY_USER_E actv_user);
void app_ble_custom_adv_stop(BLE_ADV_ACTIVITY_USER_E actv_user);
void app_ble_start_three_adv_test(void);
void app_ble_start_connectable_adv_by_custom_adv(uint16_t advInterval);
void app_ble_refresh_adv_state_by_custom_adv(uint16_t advInterval);

/**
 * app_ble_custom_adv_write_data
 *
 *    actv_user: The user of the adv activity
 *    is_custom_adv_flags: If this flag was set, custom can set adv flag by himself
 *    type : adv addr type
 *               BLE_ADV_PUBLIC_STATIC   Don't care about local_addr, just use identity ble addr.
 *               BLE_ADV_PRIVATE_STATIC  Just use local_addr.
 *               BLE_ADV_RPA    local_addr shall be set to ff:ff:ff:ff:ff:ff. If the resolving list contains
 *                              no matching entry, use rpa generated by host;otherwise use rpa generated by control.
 *               !!!If wants to use rpa, the premise is to open the macro BLE_ADV_RPA_ENABLED
 *    local_addr: The local address of this adv. ff:ff:ff:ff:ff:ff when rpa.
 *    peer_addr: If adv_type is direct adv, this param is the address of peer ble
 *    adv_interval: Adv interval
 *    adv_type: Adv type
 *    adv_mode: Adv mode
 *    tx_power_dbm: Adv tx power in dbm, range: -3~16
 *    adv_data: Adv data
 *    adv_data_size: Adv data size
 *    scan_rsp_data: Scan response data
 *    scan_rsp_data_size: Scan response data size
 */
void app_ble_custom_adv_write_data(BLE_ADV_ACTIVITY_USER_E actv_user,
                    bool is_custom_adv_flags,
                    BLE_ADV_ADDR_TYPE_E type,
                    uint8_t *local_addr,
                    ble_bdaddr_t *peer_addr,
                    uint32_t adv_interval,
                    BLE_ADV_TYPE_E adv_type,
                    ADV_MODE_E adv_mode,
                    int8_t tx_power_dbm,
                    uint8_t *adv_data, uint8_t adv_data_size,
                    uint8_t *scan_rsp_data, uint8_t scan_rsp_data_size);

/**
 * @brief Set adv filter policy after set adv param befor start adv
 * 
 * @param[in] user_index activity index
 * @param[in] filter_policy filter policy
 * 
 */
void app_ble_custom_adv_set_filter_policy(BLE_ADV_ACTIVITY_USER_E user_index, uint8_t filter_policy);

#ifdef __cplusplus
}
#endif
#endif /* __APP_CUSTOMIF_H__ */
