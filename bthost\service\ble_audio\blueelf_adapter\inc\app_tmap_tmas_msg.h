/***************************************************************************
 *
 * Copyright 2015-2021 BES.
 * All rights reserved. All unpublished rights reserved.
 *
 * No part of this work may be used or reproduced in any form or by any
 * means, or stored in a database or retrieval system, without prior written
 * permission of BES.
 *
 * Use of this work is governed by a license granted by BES.
 * This work contains confidential and proprietary information of
 * BES. which is protected by copyright, trade secret,
 * trademark and other intellectual property rights.
 *
 ****************************************************************************/
/**
 ****************************************************************************************
 * @addtogroup APP_TMAP

 * @{
 ****************************************************************************************
 */

#ifndef APP_TMAP_TMAS_MSG_H_
#define APP_TMAP_TMAS_MSG_H_
#include "bluetooth.h"

#if BLE_AUDIO_ENABLED
/**
 * @brief Set tmap tmas fole bitfiled
 * 
 * @param[in] role_bf  TMAP Role
 * 
 * @return int         status
 */
int app_tmap_tmas_set_role(uint16_t role_bf);

#endif
#endif // APP_TMAP_TMAS_MSG_H_

/// @} APP_TMAP_TMAS_MSG_H_
