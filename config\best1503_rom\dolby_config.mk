export DOLBY_AUDIO_ENABLE := 0
ifeq ($(DOLBY_AUDIO_ENABLE),1)
export USE_THIRDPARTY := 1
KBUILD_CPPFLAGS += -DDOLBY_AUDIO_ENABLE
endif

ifeq ($(ARM_CMNS),1)
ifeq ($(BES_OTA),1)
export ARM_CMNS_OTA ?= 0
ifeq ($(ARM_CMNS_OTA),1)
FLASH_REMAP := 1
COMBO_CUSBIN_IN_FLASH := 1
else
FLASH_REMAP ?= 0
endif
IS_BLE_RX_HANDLER_THREAD_ENABLED := 1
endif
endif

export SPA_AUDIO_ENABLE := 0
ifeq ($(SPA_AUDIO_ENABLE),1)
export USE_THIRDPARTY := 1
KBUILD_CPPFLAGS += -DSPA_AUDIO_ENABLE
KBUILD_CPPFLAGS += -DSPA_AUDIO_BTH
endif

#dolby decrypt use static ram
ifeq ($(SPA_AUDIO_SEC),1)
ifeq ($(DOLBY_AUDIO_DAW_ENABLE),1)
export CUSTOMER_LOAD_SRAM_TEXT_RAMX_SECTION_SIZE    := 0x4370
export CUSTOMER_LOAD_RAM_DATA_SECTION_SIZE          := 0x5B30
else
export CUSTOMER_LOAD_SRAM_TEXT_RAMX_SECTION_SIZE    := 0x41E0
export CUSTOMER_LOAD_RAM_DATA_SECTION_SIZE          := 0xF80
endif
export CUSTOMER_LOAD_ENC_DEC_RECORD_SECTION_SIZE 	:= 42*1024
endif
