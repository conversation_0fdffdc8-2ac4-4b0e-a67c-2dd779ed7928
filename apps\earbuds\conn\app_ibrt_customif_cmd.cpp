/***************************************************************************
 *
 * Copyright 2015-2019 BES.
 * All rights reserved. All unpublished rights reserved.
 *
 * No part of this work may be used or reproduced in any form or by any
 * means, or stored in a database or retrieval system, without prior written
 * permission of BES.
 *
 * Use of this work is governed by a license granted by BES.
 * This work contains confidential and proprietary information of
 * BES. which is protected by copyright, trade secret,
 * trademark and other intellectual property rights.
 *
 ****************************************************************************/
/*****************************header include********************************/
#include "string.h"
#include "app_tws_ibrt_cmd_handler.h"
#include "app_ibrt_customif_cmd.h"
#include "bts_core_if.h"
#ifdef GGEC_MDF_SDK
#include "cwm_port.h"
#include "cwm_lib.h"
#include "ggec_event_if.h"
#include "ggec_hw_hal.h"
#include "ggec_ble_msg.h"
#include "app_battery.h"
#include "cmsis_os.h"
#include "bes_me_api.h"
#include "factory_section.h"
#include "app_bt.h"
#include "app_ibrt_nvrecord.h"
#include "user_box.h"
#endif
#if defined(IBRT)

/*********************external function declaration*************************/

/*********************internal function declaration*************************/
static void app_ibrt_customif_test1_cmd_send(uint8_t *p_buff, uint16_t length);
static void app_ibrt_customif_test1_cmd_send_handler(uint16_t rsp_seq, uint8_t *p_buff, uint16_t length);

static void app_ibrt_customif_test2_cmd_send(uint8_t *p_buff, uint16_t length);
static void app_ibrt_customif_test2_cmd_send_handler(uint16_t rsp_seq, uint8_t *p_buff, uint16_t length);
static void app_ibrt_customif_test2_cmd_send_rsp_timeout_handler(uint16_t rsp_seq, uint8_t *p_buff, uint16_t length);
static void app_ibrt_customif_test2_cmd_send_rsp_handler(uint16_t rsp_seq, uint8_t *p_buff, uint16_t length);
static void app_ibrt_customif_test2_cmd_send_tx_done_handler(uint16_t cmdcode, uint16_t rsp_seq, uint8_t *ptrParam, uint16_t paramLen);

#ifdef GGEC_MDF_SDK
#ifdef GGEC_AUTOVOL_ALGO
static void app_ibrt_customif_autovol_cmd_send(uint8_t *p_buff, uint16_t length);
static void app_ibrt_customif_autovol_cmd_recive_handler(uint16_t rsp_seq, uint8_t *p_buff, uint16_t length);
#endif
#endif

static const app_tws_cmd_instance_t g_ibrt_custom_cmd_handler_table[]=
{
    {
        APP_IBRT_CUSTOM_CMD_TEST1,                              "TWS_CMD_TEST1",
        app_ibrt_customif_test1_cmd_send,
        app_ibrt_customif_test1_cmd_send_handler,               0,
        app_ibrt_custom_cmd_rsp_timeout_handler_null,           app_ibrt_custom_cmd_rsp_handler_null,
        app_ibrt_custom_cmd_tx_done_handler_null,
        APP_TWS_CMD_PRIO_0
    },
    {
        APP_IBRT_CUSTOM_CMD_TEST2,                              "TWS_CMD_TEST2",
        app_ibrt_customif_test2_cmd_send,
        app_ibrt_customif_test2_cmd_send_handler,               RSP_TIMEOUT_DEFAULT,
        app_ibrt_customif_test2_cmd_send_rsp_timeout_handler,   app_ibrt_customif_test2_cmd_send_rsp_handler,
        app_ibrt_customif_test2_cmd_send_tx_done_handler,
        APP_TWS_CMD_PRIO_0
    },

    #ifdef GGEC_MDF_SDK
    #ifdef GGEC_AUTOVOL_ALGO
    {
        APP_IBRT_CUSTOM_CMD_AUTOVOL,                              "TWS_CMD_AUTOVOL",
        app_ibrt_customif_autovol_cmd_send,
        app_ibrt_customif_autovol_cmd_recive_handler,               0,
        app_ibrt_custom_cmd_rsp_timeout_handler_null,   app_ibrt_custom_cmd_rsp_handler_null,
        app_ibrt_custom_cmd_tx_done_handler_null,
        APP_TWS_CMD_PRIO_0
    },
    #endif
    #endif
};

static app_tws_cmd_timer_instance_t *g_ibrt_custom_cmd_handler_var_table[ARRAY_SIZE(g_ibrt_custom_cmd_handler_table)];
/****************************function defination****************************/
int app_ibrt_customif_cmd_table_get(void **cmd_tbl, void ***cmd_var_tbl, uint16_t *cmd_size)
{
    *cmd_tbl = (void *)&g_ibrt_custom_cmd_handler_table;
    *cmd_size = ARRAY_SIZE(g_ibrt_custom_cmd_handler_table);
    *cmd_var_tbl = (void **)&g_ibrt_custom_cmd_handler_var_table;
    return 0;
}

void app_ibrt_customif_cmd_test(ibrt_custom_cmd_test_t *cmd_test)
{
    tws_ctrl_send_cmd(APP_IBRT_CUSTOM_CMD_TEST1, (uint8_t*)cmd_test, sizeof(ibrt_custom_cmd_test_t));
    tws_ctrl_send_cmd(APP_IBRT_CUSTOM_CMD_TEST2, (uint8_t*)cmd_test, sizeof(ibrt_custom_cmd_test_t));
}

#ifdef GGEC_MDF_SDK
#ifdef GGEC_AUTOVOL_ALGO
extern "C"  void set_noise_other_ear(float noise_dB);

static void app_ibrt_customif_autovol_cmd_send(uint8_t *p_buff, uint16_t length)
{
    app_ibrt_send_cmd_without_rsp(APP_IBRT_CUSTOM_CMD_AUTOVOL, p_buff, length);
}

static void app_ibrt_customif_autovol_cmd_recive_handler(uint16_t rsp_seq, uint8_t *p_buff, uint16_t length)
{
    EARBUDS_TRACE(1, "%s, len =%d, pbuf[0] =0x%02x", __func__, length, p_buff[0]);
    if (length == 4) {
        printf("%s noise_other_ear : %f\n" , __func__, (double)*((float *)p_buff));
        set_noise_other_ear(*((float *)p_buff));
    }
}

#endif
#endif

static void app_ibrt_customif_test1_cmd_send(uint8_t *p_buff, uint16_t length)
{
    app_ibrt_send_cmd_without_rsp(APP_IBRT_CUSTOM_CMD_TEST1, p_buff, length);
    EARBUDS_TRACE(1, "%s", __func__);
}

#ifdef GGEC_MDF_SDK
extern uint8_t Wear_ears_flag[3];
extern uint8_t hfp_volume_sync;
#endif

static void app_ibrt_customif_test1_cmd_send_handler(uint16_t rsp_seq, uint8_t *p_buff, uint16_t length)
{
    #ifdef GGEC_MDF_SDK
    #ifdef GGEC_CWM_ALGO
    if (length == 50) {
        cwm_set_earside_sync_val((const uint8_t *)p_buff, length);
    }
    EARBUDS_TRACE(1, "%s, len =%d, pbuf[0] =0x%02x", __func__, length, p_buff[0]);
    #endif
    #else
    EARBUDS_TRACE(1, "%s", __func__);
    #endif

    #ifdef GGEC_MDF_SDK
    if (length == 2) {
        uint16_t evt = *((uint16_t *)p_buff);
        EARBUDS_TRACE(1, "%s, evt = 0x%04x",__func__, evt);
        GetMqApi()->SendTopic(KEY_TOPIC, evt, NULL, 0);
    } else if (length == 1) {
        uint8_t evt_wear = *((uint8_t *)p_buff);
        EARBUDS_TRACE(1, "%s, evt = 0x%02x",__func__, evt_wear);
        switch(evt_wear){
            #ifdef GGEC_CWM_ALGO
            case 0x0A: 
                Wear_ears_flag[SLAVE_FLAG]=1;
                EARBUDS_TRACE(1, "CAP_KEY_ON_EAR_EVT[crj],evt = 0x%02x",evt_wear);
                GetMqApi()->SendTopic(KEY_TOPIC, SLAVE_CAP_KEY_ON_EAR_EVT, NULL, 0);
            break;

            case 0x0C: 
                Wear_ears_flag[SLAVE_FLAG]=0;
                EARBUDS_TRACE(1, "CAP_KEY_OFF_EAR_EVT_0[crj],evt = 0x%02x",evt_wear);
                GetMqApi()->SendTopic(KEY_TOPIC, SLAVE_CAP_KEY_OFF_EAR_EVT, NULL, 0);
                break;
            #endif
        }
    } else if (length == 3) {
        hfp_volume_sync=*((uint8_t *)p_buff);
        EARBUDS_TRACE(1, "[crj_debug]hfp_vloume_sync: = %d",hfp_volume_sync);
        GetMqApi()->SendTopic(KEY_TOPIC, BT_HFP_VLOUME_SYNC, NULL, 0);  
    } else if (length == sizeof(user_box_tws_master_info)) {
        user_box_tws_master_info *msg = (user_box_tws_master_info *)p_buff;
        if (msg->is_from_slave) {
            EARBUDS_TRACE(1, "%s: master enter pairing now!!!", __func__);
            user_box_enter_bt_pairing();
        }
    } else {
        if (length == sizeof(hlai_msg_t)) {
            hlai_msg_t *msg = (hlai_msg_t *)p_buff;
            EARBUDS_TRACE(1, "%s: msg type = %u", __func__, msg->header.msg_type);
            switch (msg->header.msg_type) {
                case HLAI_MSG_TYPE_BATT: {
                    enum HLAI_GET_BATTERY_TYPE_T type = (enum HLAI_GET_BATTERY_TYPE_T)msg->data[0];
                    if (type == HLAI_GET_BATTERY_LEFT_ERABUD || type == HLAI_GET_BATTERY_RIGHT_ERABUD) {
                        uint16_t currvolt = 0;
                        app_battery_get_info(&currvolt, NULL, NULL);
                        uint8_t battery = ggec_factory_get_bat_level(currvolt);
                        msg->data[1] = battery;
                        msg->header.data_len_h = 0;
                        msg->header.data_len_l = 2;
                        EARBUDS_TRACE(1, "%s: send peer battery, value = %u", __func__, battery);
                        msg->data[0] = HLAI_GET_BATTERY_RSP + type;
                        tws_ctrl_send_cmd(APP_IBRT_CUSTOM_CMD_TEST1, p_buff, length);
                    } else if (type == HLAI_GET_BATTERY_CHARGECASE) {
                        EARBUDS_TRACE(1, "%s: peer chargecase bat = %u", __func__, ggec_get_chargecase_battery_value());
                        msg->data[0] = HLAI_GET_BATTERY_RSP + type;
                        msg->data[1] = ggec_get_chargecase_battery_value();
                        msg->header.data_len_h = 0;
                        msg->header.data_len_l = 2;
                        tws_ctrl_send_cmd(APP_IBRT_CUSTOM_CMD_TEST1, p_buff, length);
                    } else if (type >= HLAI_GET_BATTERY_RSP) {
                        EARBUDS_TRACE(1, "%s: send reply msg battery rsp = %u", __func__, msg->data[1]);
                        msg->data[0] = msg->data[0] - HLAI_GET_BATTERY_RSP;
                        ggec_app_send_reply_msg(msg);
                    }
                    break;
                }

                case HLAI_MSG_TYPE_SET_BT_NAME: {
                    if (msg->data[0] == HLAI_TWS_COMMUNICATION_NEED_RSP) {
                        uint8_t name_len = msg->header.data_len_l - 1;
                        uint8_t bt_name[HLAI_APP_MAX_SET_BT_NAME_LEN] = {0};
                        memcpy(bt_name, msg->data + 1, sizeof(uint8_t) * name_len);
                        EARBUDS_TRACE(1, "%s: set bt name, len = %u, name = %s", __func__, name_len, bt_name);
                        app_bt_set_access_mode(BTIF_BAM_NOT_ACCESSIBLE);
                        bes_bt_me_set_bt_name((char *)bt_name, name_len + 1);
                        factory_section_set_bt_name((char *)bt_name, name_len + 1);
                        msg->data[0] = HLAI_TWS_COMMUNICATION_WITHOUT_RSP;
                        tws_ctrl_send_cmd(APP_IBRT_CUSTOM_CMD_TEST1, p_buff, length);
                        app_bt_set_access_mode(BTIF_BAM_GENERAL_ACCESSIBLE);
                    } else {
                        EARBUDS_TRACE(1, "%s: send reply msg set_by_name rsp = %u", __func__, msg->data[0]);
                        app_bt_set_access_mode(BTIF_BAM_GENERAL_ACCESSIBLE);
                        msg->data[0] = HLAI_SET_BT_NAME_SUCCESS;
                        msg->header.data_len_h = 0;
                        msg->header.data_len_l = 1;
                        ggec_app_send_reply_msg(msg);
                    }
                    break;
                }

                case HLAI_MSG_TYPE_CLEAN_BT_RECORD: {
                    if (msg->data[0] == HLAI_TWS_COMMUNICATION_NEED_RSP) {
                        EARBUDS_TRACE(1, "%s: slave earbud clean bt record!", __func__);
                        app_bt_set_access_mode(BTIF_BAM_NOT_ACCESSIBLE);
                        app_ibrt_nvrecord_delete_all_mobile_record();
                        nv_record_flash_flush();
                        msg->data[0] = HLAI_TWS_COMMUNICATION_WITHOUT_RSP;
                        tws_ctrl_send_cmd(APP_IBRT_CUSTOM_CMD_TEST1, p_buff, length);
                        app_bt_set_access_mode(BTIF_BAM_GENERAL_ACCESSIBLE);
                    } else {
                        EARBUDS_TRACE(1, "%s: clean bt record success!", __func__);
                        app_bt_set_access_mode(BTIF_BAM_GENERAL_ACCESSIBLE);
                        msg->data[0] = HLAI_CLEAN_BT_RECORD_SUCCESS;
                        msg->header.data_len_h = 0;
                        msg->header.data_len_l = 1;
                        ggec_app_send_reply_msg(msg);
                    }
                    break;
                }

                case HLAI_MSG_TYPE_FIND_EARBUD_MUSIC: {
                    enum HLAI_FIND_EARBUD_MUSIC_TYPE_T type = (enum HLAI_FIND_EARBUD_MUSIC_TYPE_T)msg->data[0];
                    if (type == HLAI_FIND_EARBUD_MUSIC_LEFT || type == HLAI_FIND_EARBUD_MUSIC_RIGHT) {
                        EARBUDS_TRACE(1, "%s: peer find earbud!!!", __func__);
                        EARBUDS_TRACE(1, "%s: side = %u, is_on = %u", __func__, type, msg->data[1]);
                        msg->data[0] = HLAI_FIND_EARBUD_MUSIC_RSP + type;
                        ggec_find_earbud_local_player(msg->data[1] == 1 ? true : false, true);
                        tws_ctrl_send_cmd(APP_IBRT_CUSTOM_CMD_TEST1, p_buff, length);
                    } else if (type >= HLAI_FIND_EARBUD_MUSIC_RSP) {
                        EARBUDS_TRACE(1, "%s: receive reply msg find_earbud rsp = %u", __func__, msg->data[0]);
                        msg->data[0] = msg->data[0] - HLAI_FIND_EARBUD_MUSIC_RSP;
                        ggec_app_send_reply_msg(msg);
                    }
                   break;
               }

                 case HLAI_MSG_TYPE_EARBUD_IN_CHARGECASE_STATUS: {
                     enum HLAI_TWS_COMMUNICATION_TYPE_T type = (enum HLAI_TWS_COMMUNICATION_TYPE_T)msg->data[0];
                     if (type == HLAI_TWS_COMMUNICATION_NEED_RSP) {
                         EARBUDS_TRACE(1, "%s: peer is_in_chargecase = %u", __func__, ggec_get_earbud_in_chargecase_status());
                         msg->data[0] = HLAI_TWS_COMMUNICATION_WITHOUT_RSP + ggec_get_earbud_in_chargecase_status();
                         tws_ctrl_send_cmd(APP_IBRT_CUSTOM_CMD_TEST1, p_buff, length);
                     } else {
                         msg->data[0] = msg->data[0] - HLAI_TWS_COMMUNICATION_WITHOUT_RSP;
                         EARBUDS_TRACE(1, "%s: receive peer info, data = %u", __func__, msg->data[0]);
                         ggec_app_send_reply_msg(msg);
                     }
                    break;
                }

                  case HLAI_MSG_TYPE_CHARGECASE_CHARGING_STATUS: {
                      enum HLAI_TWS_COMMUNICATION_TYPE_T type = (enum HLAI_TWS_COMMUNICATION_TYPE_T)msg->data[0];
                      if (type == HLAI_TWS_COMMUNICATION_NEED_RSP) {
                          EARBUDS_TRACE(1, "%s: peer is_charing = %u", __func__, ggec_get_chargecase_charging_status());
                          msg->data[0] = HLAI_TWS_COMMUNICATION_WITHOUT_RSP + ggec_get_chargecase_charging_status();
                          tws_ctrl_send_cmd(APP_IBRT_CUSTOM_CMD_TEST1, p_buff, length);
                      } else {
                          msg->data[0] = msg->data[0] - HLAI_TWS_COMMUNICATION_WITHOUT_RSP;
                          EARBUDS_TRACE(1, "%s: receive peer is_charging, data = %u", __func__, msg->data[0]);
                          ggec_app_send_reply_msg(msg);
                      }
                     break;
                 }

                default:
                    break;
            }
        } else if (length == sizeof(s_ggec_ble_app)) { // receive app set info from master
            EARBUDS_TRACE(1, "%s: receive app set info from master", __func__);
            s_ggec_ble_app master_info = *((s_ggec_ble_app *)p_buff);
            s_ggec_ble_app slave_info = ggec_get_ble_app_info();
            ggec_master_sync_slave_app_info(master_info, slave_info);
        }
    }
    #endif
}

static void app_ibrt_customif_test2_cmd_send(uint8_t *p_buff, uint16_t length)
{
    EARBUDS_TRACE(1, "%s", __func__);
    app_ibrt_send_cmd_with_rsp(APP_IBRT_CUSTOM_CMD_TEST2, p_buff, length);
}

static void app_ibrt_customif_test2_cmd_send_handler(uint16_t rsp_seq, uint8_t *p_buff, uint16_t length)
{
    #if 0//def GGEC_MDF_SDK
    EARBUDS_TRACE(1, "%s, len =%d, pbuf[0] =0x%02x", __func__, length, p_buff[0]);
    if (length == 2) {
        uint16_t evt = *((uint16_t *)p_buff);
        EARBUDS_TRACE(1, "%s, evt = 0x%04x",__func__, evt);
        GetMqApi()->SendTopic(KEY_TOPIC, evt, NULL, 0);
    } else if (length == 1) {
        uint8_t evt_wear = *((uint8_t *)p_buff);
        EARBUDS_TRACE(1, "%s, evt = 0x%02x",__func__, evt_wear);
        switch(evt_wear){
            #ifdef GGEC_CWM_ALGO
            case 0x0A: 
                Wear_ears_flag[SLAVE_FLAG]=1;
                EARBUDS_TRACE(1, "CAP_KEY_ON_EAR_EVT[crj],evt = 0x%02x",evt_wear);
                GetMqApi()->SendTopic(KEY_TOPIC, SLAVE_CAP_KEY_ON_EAR_EVT, NULL, 0);
            break;

            case 0x0C: 
                Wear_ears_flag[SLAVE_FLAG]=0;
                EARBUDS_TRACE(1, "CAP_KEY_OFF_EAR_EVT_0[crj],evt = 0x%02x",evt_wear);
                GetMqApi()->SendTopic(KEY_TOPIC, SLAVE_CAP_KEY_OFF_EAR_EVT, NULL, 0);
                break;
            #endif
        }
    } else {
        if (length == sizeof(hlai_msg_t)) {
            uint16_t currvolt = 0;
            app_battery_get_info(&currvolt, NULL, NULL);
            uint8_t battery = ggec_factory_get_bat_level(currvolt);
            ((hlai_msg_t *)p_buff)->data[1] = battery;
            ((hlai_msg_t *)p_buff)->header.data_len_h = 0;
            ((hlai_msg_t *)p_buff)->header.data_len_l = 2;
            EARBUDS_TRACE(1, "%s: send peer battery, value = %u", __func__, battery);
            tws_ctrl_send_rsp(APP_IBRT_CUSTOM_CMD_TEST2, rsp_seq, p_buff, length);
        } else if (length == sizeof(s_ggec_ble_app)) { // receive app set info from master
            EARBUDS_TRACE(1, "%s: receive app set info from master", __func__);
            s_ggec_ble_app master_info = *((s_ggec_ble_app *)p_buff);
            s_ggec_ble_app slave_info = ggec_get_ble_app_info();
            ggec_master_sync_slave_app_info(master_info, slave_info);
        }
    }
    #else
    EARBUDS_TRACE(1, "%s", __func__);
    tws_ctrl_send_rsp(APP_IBRT_CUSTOM_CMD_TEST2, rsp_seq, p_buff, length);
    #endif
}

#ifdef GGEC_MDF_SDK
static osMutexId test2_cmd_mutex_id = NULL;
osMutexDef(test2_cmd_mutex);
static int test2_cmd_lock(bool is_lock)
{
    if (test2_cmd_mutex_id == NULL) {
        test2_cmd_mutex_id = osMutexCreate(osMutex(test2_cmd_mutex));
    }
    if (test2_cmd_mutex_id == NULL) {
        EARBUDS_TRACE(1, " create test2_cmd_mutex_id failed");
        return -1;
    }
    if (is_lock) {
        osMutexWait(test2_cmd_mutex_id, osWaitForever);
    } else {
        osMutexRelease(test2_cmd_mutex_id);
    }
    return 0;
}

void ggec_test2_cmd_send(uint8_t *p_buff, uint16_t length)
{
    EARBUDS_TRACE(1, "%s", __func__);
    #if 0
    if (test2_cmd_lock(true) != 0) {
        EARBUDS_TRACE(1, "%s: lock failed", __func__);
        return;
    }
    #endif
    if (user_box_get_box_action() == CLOSE_BOX_ACTION) {
        EARBUDS_TRACE(1, "%s: box has closed without send cmd!!!", __func__);
        return;
    }
    
    app_ibrt_send_cmd_without_rsp(APP_IBRT_CUSTOM_CMD_TEST1, p_buff, length);
    //tws_ctrl_send_cmd(APP_IBRT_CUSTOM_CMD_TEST1, p_buff, length);
}
#endif

static void app_ibrt_customif_test2_cmd_send_rsp_timeout_handler(uint16_t rsp_seq, uint8_t *p_buff, uint16_t length)
{
    EARBUDS_TRACE(1, "%s", __func__);
    #ifdef GGEC_MDF_SDK
    test2_cmd_lock(false);
    #endif
}

static void app_ibrt_customif_test2_cmd_send_rsp_handler(uint16_t rsp_seq, uint8_t *p_buff, uint16_t length)
{
    EARBUDS_TRACE(1, "%s: length = %u", __func__, length);
}

static void app_ibrt_customif_test2_cmd_send_tx_done_handler(uint16_t cmdcode, uint16_t rsp_seq, uint8_t *ptrParam, uint16_t paramLen)
{
    EARBUDS_TRACE(1, "%s", __func__);
    #ifdef GGEC_MDF_SDK
    test2_cmd_lock(false);
    #endif
}
#endif /* IBRT */
