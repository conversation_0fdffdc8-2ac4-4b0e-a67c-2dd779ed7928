############ RAM OPTIMIZE ############
export USE_BASIC_THREADS ?= 1
export SCO_OPTIMIZE_FOR_RAM ?= 1
export BT_USE_COHEAP_ALLOC  ?= 1
export UNIFY_HEAP_ENABLED ?= 1
export FAST_RAM_OPTIMIZE ?= 0
export USE_OVERLAY_TXT_GAP ?= 0
########## RAM OPTIMIZE END ##########

ifeq ($(A2DP_CP_ACCEL),1)
ifeq ($(GGEC_AUTOVOL_ALGO),1)
export RAMCP_SIZE :=  0x4A000
export RAMCPX_SIZE := 0x9000
else
export RAMCP_SIZE :=  0x4e000
export RAMCPX_SIZE := 0x0e000
endif

else
ifeq ($(CP_INTR_RELAY),1)
export RAMCP_SIZE :=  0x40000
export RAMCPX_SIZE := 0x18000
else
export RAMCP_SIZE := 0
export RAMCPX_SIZE := 0
endif
endif

ifeq ($(GGEC_AUTOVOL_ALGO),1)
export FAST_XRAM_SECTION_SIZE := 0x8000
else
export FAST_XRAM_SECTION_SIZE ?= 0x2000
endif


ifeq ($(USE_BASIC_THREADS),1)
ifeq ($(TOTA_v2),1)
BESBT_STACK_SIZE := 1024*4
else
ifeq ($(BLE_AUDIO_ENABLED),1)
BESBT_STACK_SIZE := 1024*3
else
BESBT_STACK_SIZE := 1024*3
endif
endif
KBUILD_CPPFLAGS += -DAF_STACK_SIZE=1024*3+512
ifeq ($(DIRAC_AUDIO_ENABLE),1)
KBUILD_CPPFLAGS += -DAPP_THREAD_STACK_SIZE=1024*3
else
KBUILD_CPPFLAGS += -DAPP_THREAD_STACK_SIZE=1024*3
endif
KBUILD_CPPFLAGS += -DINTERSYS_STACK_SIZE=1024
export OS_TIMER_THREAD_STACK_SIZE ?= 0x800
endif
export OS_DYNAMIC_MEM_SIZE ?= 0x9000 #GGEC HH 0x6000 --> 0x9000
export CP_IN_CACHE_SIZE := 1024*3

KBUILD_CPPFLAGS += -DFAST_XRAM_SECTION_SIZE=$(FAST_XRAM_SECTION_SIZE)
KBUILD_CPPFLAGS += -DRAMCP_SIZE=$(RAMCP_SIZE)
KBUILD_CPPFLAGS += -DRAMCPX_SIZE=$(RAMCPX_SIZE)
KBUILD_CPPFLAGS += -DCP_IN_CACHE_SIZE=$(CP_IN_CACHE_SIZE)
