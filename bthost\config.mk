# -------------------------------------------
# BR/EDR features
# -------------------------------------------
export BT_USE_COHEAP_ALLOC ?= 1
ifeq ($(BT_USE_COHEAP_ALLOC),1)
KBUILD_CPPFLAGS += -DBT_USE_COHEAP_ALLOC
endif

ifeq ($(SOURCE_TRACE_RX),1)
KBUILD_CPPFLAGS += -D__SOURCE_TRACE_RX__
endif

ifeq ($(BT_MULTI_SOURCE),1)
KBUILD_CPPFLAGS += -DBT_MULTI_SOURCE
endif

#----- A2DP features -----#
ifeq ($(A2DP_AVDTP_CP),1)
KBUILD_CPPFLAGS += -D__A2DP_AVDTP_CP__
endif

export BT_DONT_AUTO_PLAY_BG_A2DP ?= 0
ifeq ($(BT_DONT_AUTO_PLAY_BG_A2DP),1)
KBUILD_CPPFLAGS += -DBT_DONT_AUTO_PLAY_BG_A2DP
endif

export A2DP_PROMPT_PLAY_ONLY_AVRCP_PLAY_RECEIVED ?= 1
ifeq ($(A2DP_PROMPT_PLAY_ONLY_AVRCP_PLAY_RECEIVED),1)
KBUILD_CPPFLAGS += -DA2DP_PROMPT_PLAY_ONLY_AVRCP_PLAY_RECEIVED
endif

export A2DP_DELAY_PROMPT_PLAY ?= 0
ifeq ($(A2DP_DELAY_PROMPT_PLAY),1)
KBUILD_CPPFLAGS += -DA2DP_DELAY_PROMPT_PLAY
endif

export BT_MUTE_A2DP ?= 0 #GGEC_MDF_SDK default is 1
ifeq ($(BT_MUTE_A2DP),1)
KBUILD_CPPFLAGS += -DBT_MUTE_A2DP
endif

export BT_PAUSE_A2DP ?= 0
ifeq ($(BT_PAUSE_A2DP),1)
KBUILD_CPPFLAGS += -DBT_PAUSE_A2DP
endif

export BT_CLOSE_A2DP ?= 1 #GGEC_MDF_SDK default is 0
ifeq ($(BT_CLOSE_A2DP),1)
KBUILD_CPPFLAGS += -DBT_CLOSE_A2DP
endif

export A2DP_LC3_HR ?= 0
ifeq ($(A2DP_LC3_HR),1)
KBUILD_CPPFLAGS += -DA2DP_LC3_HR
endif

ifeq ($(A2DP_SOURCE_AUTO_START),1)
KBUILD_CPPFLAGS += -DA2DP_SOURCE_AUTO_START
endif

ifeq ($(BT_SOURCE),1)
KBUILD_CPPFLAGS += -DBT_AVRCP_TG_ROLE
KBUILD_CPPFLAGS += -DBT_A2DP_SRC_ROLE
endif

ifeq ($(BT_A2DP_SINK_SOURCE_BOTH_SUPPORT),1)
KBUILD_CPPFLAGS += -DBT_A2DP_SINK_SOURCE_BOTH_SUPPORT
endif

ifeq ($(A2DP_SOURCE_AAC_ON),1)
KBUILD_CPPFLAGS += -DA2DP_SOURCE_AAC_ON
KBUILD_CPPFLAGS += -D__A2DP_AVDTP_CP__
export SMF_A2DP_SOURCE_AAC_ON ?= 0
ifeq ($(SMF_A2DP_SOURCE_AAC_ON),1)
KBUILD_CPPFLAGS += -DSMF_A2DP_SOURCE_AAC_ON
endif
endif

export BT_HFP_DONT_SUPPORT_CLI_FEATURE ?= 0
ifeq ($(BT_HFP_DONT_SUPPORT_CLI_FEATURE),1)
KBUILD_CPPFLAGS += -DBT_HFP_DONT_SUPPORT_CLI_FEATURE
endif

export BT_HFP_DONT_SUPPORT_ENHANCED_CALL_FEATURE ?= 0
ifeq ($(BT_HFP_DONT_SUPPORT_ENHANCED_CALL_FEATURE),1)
KBUILD_CPPFLAGS += -DBT_HFP_DONT_SUPPORT_ENHANCED_CALL_FEATURE
endif

export BT_HFP_DONT_SUPPORT_3WAY_CALL_FEATURE ?= 0
ifeq ($(BT_HFP_DONT_SUPPORT_3WAY_CALL_FEATURE),1)
KBUILD_CPPFLAGS += -DBT_HFP_DONT_SUPPORT_3WAY_CALL_FEATURE
endif

export BT_HFP_SUPPORT_HF_INDICATORS_FEATURE ?= 0
ifeq ($(BT_HFP_SUPPORT_HF_INDICATORS_FEATURE),1)
KBUILD_CPPFLAGS += -DBT_HFP_SUPPORT_HF_INDICATORS_FEATURE
endif

export BT_HFP_DONT_SUPPORT_APPLE_HF_AT_COMMAND ?= 0
ifeq ($(BT_HFP_DONT_SUPPORT_APPLE_HF_AT_COMMAND),1)
KBUILD_CPPFLAGS += -DBT_HFP_DONT_SUPPORT_APPLE_HF_AT_COMMAND
endif

export SCO_FORCE_CVSD ?= 0
ifeq ($(SCO_FORCE_CVSD),1)
KBUILD_CPPFLAGS += -DSCO_FORCE_CVSD
endif

ifeq ($(AUTO_ACCEPT_SECOND_SCO),1)
KBUILD_CPPFLAGS += -DAUTO_ACCEPT_SECOND_SCO
endif

ifeq ($(BT_HFP_AG_ROLE),1)
KBUILD_CPPFLAGS += -DBT_HFP_AG_ROLE

ifeq ($(HFP_AG_SCO_AUTO_CONN),1)
KBUILD_CPPFLAGS += -DHFP_AG_SCO_AUTO_CONN
endif

ifeq ($(HFP_AG_TEST),1)
KBUILD_CPPFLAGS += -DHFP_AG_TEST
endif
endif # BT_HFP_AG_ROLE

export BT_LOCAL_PLAYER_DISABLE ?= 0
ifeq ($(BT_LOCAL_PLAYER_DISABLE),1)
KBUILD_CPPFLAGS += -DBT_LOCAL_PLAYER_DISABLE
endif

#----- Other features -----#
export SPP_SERVICE_NUM ?= 5
KBUILD_CPPFLAGS += -DSPP_SERVICE_NUM=$(SPP_SERVICE_NUM)

export BT_OPP_SUPPORT ?= 0
ifeq ($(BT_OPP_SUPPORT),1)
KBUILD_CPPFLAGS += -DBT_OPP_SUPPORT
KBUILD_CPPFLAGS += -DBT_OBEX_SUPPORT
endif

export BT_PBAP_SUPPORT ?= 0
ifeq ($(BT_PBAP_SUPPORT),1)
KBUILD_CPPFLAGS += -DBT_PBAP_SUPPORT
KBUILD_CPPFLAGS += -DBT_OBEX_SUPPORT
endif

export BT_BROWSING_SUPPORT ?= 0
ifeq ($(BT_BROWSING_SUPPORT),1)
KBUILD_CPPFLAGS += -DBT_OBEX_SUPPORT
KBUILD_CPPFLAGS += -DBT_BROWSING_SUPPORT
KBUILD_CPPFLAGS += -DAVRCP_TRACK_CHANGED
endif

export BT_PAN_SUPPORT ?= 0
ifeq ($(BT_PAN_SUPPORT),1)
KBUILD_CPPFLAGS += -DBT_PAN_SUPPORT
KBUILD_CPPFLAGS += -DBT_BNEP_SUPPORT
endif

ifeq ($(HSP_ENABLE),1)
KBUILD_CPPFLAGS += -DHSP_ENABLE
endif

export BT_STACK_LOG_DISABLE ?= 0
ifeq ($(BT_STACK_LOG_DISABLE),1)
KBUILD_CPPFLAGS += -DBT_STACK_LOG_DISABLE
endif

export A2DP_STREAM_DETECT_NO_DECODE ?= 0
ifeq ($(A2DP_STREAM_DETECT_NO_DECODE),1)
KBUILD_CPPFLAGS += -DA2DP_STREAM_DETECT_NO_DECODE
endif

export BT_KEEP_ONE_STREAM_CLOSE_CONNECTED_A2DP ?= 0
ifeq ($(BT_KEEP_ONE_STREAM_CLOSE_CONNECTED_A2DP),1)
KBUILD_CPPFLAGS += -DBT_KEEP_ONE_STREAM_CLOSE_CONNECTED_A2DP
endif

export BT_DONT_AUTO_REPORT_DELAY_REPORT ?= 0
ifeq ($(BT_DONT_AUTO_REPORT_DELAY_REPORT),1)
KBUILD_CPPFLAGS += -DBT_DONT_AUTO_REPORT_DELAY_REPORT
endif

export BT_DISC_ACL_AFTER_AUTH_KEY_MISSING ?= 1  #ggec mdf SDK 
ifeq ($(BT_DISC_ACL_AFTER_AUTH_KEY_MISSING),1)
KBUILD_CPPFLAGS += -DBT_DISC_ACL_AFTER_AUTH_KEY_MISSING
endif

export BT_DISABLE_INITIAL_ROLE_SWITCH ?= 0
ifeq ($(BT_DISABLE_INITIAL_ROLE_SWITCH),1)
KBUILD_CPPFLAGS += -DBT_DISABLE_INITIAL_ROLE_SWITCH
endif

export USE_PAGE_SCAN_REPETITION_MODE_R1 ?= 0
ifeq ($(USE_PAGE_SCAN_REPETITION_MODE_R1),1)
KBUILD_CPPFLAGS += -DUSE_PAGE_SCAN_REPETITION_MODE_R1
endif

export SUPPORT_ME_MEDIATOR ?= 0
ifeq ($(CHIP_HAS_SUPPORT_ME_MEDIATOR),1)
export SUPPORT_ME_MEDIATOR := 1
KBUILD_CPPFLAGS += -DSUPPORT_ME_MEDIATOR
endif

# -------------------------------------------
# BLE features
# -------------------------------------------
ifeq ($(BLE),1)
export mHDT_LE_SUPPORT ?= 0
ifeq ($(mHDT_LE_SUPPORT),1)
KBUILD_CPPFLAGS += -DmHDT_LE_SUPPORT=1
endif

export HDT_LE_SUPPORT ?= 0
ifeq ($(HDT_LE_SUPPORT),1)
KBUILD_CPPFLAGS += -DHDT_LE_SUPPORT=1
endif

# BLE custom if module
export IS_BLE_CUSTOM_IF_ENABLED ?= 1
ifeq ($(IS_BLE_CUSTOM_IF_ENABLED), 1)
KBUILD_CPPFLAGS += -DIS_BLE_CUSTOM_IF_ENABLED
endif

# BLE profiles enable flag
export IS_USE_CUSTOM_BLE_DATAPATH_PROFILE_UUID_ENABLED ?= 0
ifeq ($(IS_USE_CUSTOM_BLE_DATAPATH_PROFILE_UUID_ENABLED), 1)
KBUILD_CPPFLAGS += -DIS_USE_CUSTOM_BLE_DATAPATH_PROFILE_UUID_ENABLED
endif

export BLE_PERIPHERAL_ONLY ?= 0
ifeq ($(BLE_PERIPHERAL_ONLY),1)
KBUILD_CPPFLAGS += -DBLE_PERIPHERAL_ONLY
endif #BLE_PERIPHERAL_ONLY

export BLE_GATT_CLIENT_DISABLE ?= 0
ifeq ($(BLE_GATT_CLIENT_DISABLE),1)
KBUILD_CPPFLAGS += -DBLE_GATT_CLIENT_DISABLE
endif #BLE_GATT_CLIENT_DISABLE

export BLE_PERIODIC_ADV_DISABLE ?= 0
ifeq ($(BLE_PERIODIC_ADV_DISABLE),1)
KBUILD_CPPFLAGS += -DBLE_PERIODIC_ADV_DISABLE
endif #BLE_PERIODIC_ADV_DISABLE

export BT_LOG_SIMPLIFY ?= 0
ifeq ($(BT_LOG_SIMPLIFY),1)
KBUILD_CPPFLAGS += -DBT_LOG_SIMPLIFY
endif #BT_LOG_SIMPLIFY

export BLE_GATT_CLIENT_CACHE ?= 1
ifeq ($(BLE_GATT_CLIENT_CACHE),1)
KBUILD_CPPFLAGS += -DBLE_GATT_CLIENT_CACHE
endif #BLE_GATT_CLIENT_CACHE

export BLE_USE_TWS_SYNC ?= 1
ifeq ($(BLE_USE_TWS_SYNC),1)
KBUILD_CPPFLAGS += -DBLE_USE_TWS_SYNC
endif #BLE_USE_TWS_SYNC

# BLE_ADV_RPA_ENABLED shall always be 1. if not, RPA is disabled.
export BLE_ADV_RPA_ENABLED ?= 1
export BLE_ADV_REGENERATE_NEW_RPA_DURATION ?= 0
ifeq ($(BLE_ADV_RPA_ENABLED),1)
KBUILD_CPPFLAGS += -DBLE_ADV_RPA_ENABLED
KBUILD_CPPFLAGS += -DBLE_ADV_REGENERATE_NEW_RPA_DURATION=60*15
endif

# Microsoft swift pair
export SWIFT_ENABLE ?= 0
ifeq ($(SWIFT_ENABLE),1)
KBUILD_CPPFLAGS += -DSWIFT_ENABLED
endif # SWIFT_ENABLE

export BLE_SEC_ACCEPT_BY_CUSTOMER ?= 0
ifeq ($(BLE_SEC_ACCEPT_BY_CUSTOMER),1)
KBUILD_CPPFLAGS += -DBLE_SEC_ACCEPT_BY_CUSTOMER
endif

export IS_BLE_ACTIVITY_COUNT_MORE_THAN_THREE ?= 0
ifeq ($(IS_BLE_ACTIVITY_COUNT_MORE_THAN_THREE),1)
KBUILD_CPPFLAGS += -DIS_BLE_ACTIVITY_COUNT_MORE_THAN_THREE
endif

export BLE_AOB_VOLUME_SYNC_ENABLED ?= 0
ifeq ($(BLE_AOB_VOLUME_SYNC_ENABLED),1)
KBUILD_CPPFLAGS += -DBLE_AOB_VOLUME_SYNC_ENABLED
endif

#----- BLE profile features -----#
KBUILD_CPPFLAGS += -DCFG_APP_DATAPATH_SERVER

## ANC service
export BLE_PRF_ANCS ?= 0
ifeq ($(BLE_PRF_ANCS),1)
KBUILD_CPPFLAGS += -DANCS_ENABLED
endif
export BLE_PRF_ANCC ?= 0
ifeq ($(BLE_PRF_ANCC),1)
KBUILD_CPPFLAGS += -DANCC_ENABLED
endif

# AMS service
export BLE_PRF_AMS ?= 0
ifeq ($(BLE_PRF_AMS),1)
KBUILD_CPPFLAGS += -DAMS_ENABLED
endif
export BLE_PRF_AMSC ?= 0
ifeq ($(BLE_PRF_AMSC),1)
KBUILD_CPPFLAGS += -DAMSC_ENABLED
endif

# BMS service
export BLE_PRF_BMS ?= 0
ifeq ($(BLE_PRF_BMS),1)
KBUILD_CPPFLAGS += -DBMS_ENABLED
endif
export BLE_PRF_BMSC ?= 0
ifeq ($(BLE_PRF_BMSC),1)
KBUILD_CPPFLAGS += -DBMSC_ENABLED
endif

## HID service
export BLE_PRF_HID ?= 0
ifeq ($(BLE_PRF_HID),1)
KBUILD_CPPFLAGS += -DBLE_HID_ENABLE
endif
export BLE_PRF_HID_HOST ?= 0
ifeq ($(BLE_PRF_HID_HOST),1)
KBUILD_CPPFLAGS += -DBLE_HID_HOST
endif

## HID OVER GATT PROFILE
export BLE_PRF_HOGPBH ?= 0
ifeq ($(BLE_PRF_HOGPBH),1)
KBUILD_CPPFLAGS += -DBLE_HOGPBH_ENABLE
KBUILD_CPPFLAGS += -DBLE_BATT_CLIENT_ENABLE
KBUILD_CPPFLAGS += -DBLE_DISC_ENABLE
endif
export BLE_PRF_HOGPRH ?= 0
ifeq ($(BLE_PRF_HOGPRH),1)
KBUILD_CPPFLAGS += -DBLE_HOGPRH_ENABLE
KBUILD_CPPFLAGS += -DBLE_BATT_CLIENT_ENABLE
KBUILD_CPPFLAGS += -DBLE_DISC_ENABLE
endif

## BATT service
export BLE_PRF_BATT ?= 0
ifeq ($(BLE_BATT),1)
KBUILD_CPPFLAGS += -DBLE_BATT_ENABLE
endif

## DISS service
export BLE_PRF_DISS ?= 0
ifeq ($(BLE_PRF_DISS),1)
KBUILD_CPPFLAGS += -DBLE_DISS_ENABLE
endif

## IAS service
export BLE_PRF_IAS ?= 0
ifeq ($(BLE_PRF_IAS),1)
KBUILD_CPPFLAGS += -DBLE_IAS_ENABLED
endif

## Cycling Power Sensor
export BLE_CPW_SENSOR ?= 0
ifeq ($(BLE_CPW_SENSOR),1)
KBUILD_CPPFLAGS += -DBLE_CPS_ENABLED
endif

## Cycling Power Sensor
export BLE_CPW_CONTROLLER ?= 0
ifeq ($(BLE_CPW_CONTROLLER),1)
KBUILD_CPPFLAGS += -DBLE_CPC_ENABLED
endif

## Fitness Machine Service
export BLE_FTMS ?= 0
ifeq ($(BLE_FTMS),1)
export BLE_UDP_ENABLE ?= 1
ifeq ($(BLE_UDP_ENABLE),1)
KBUILD_CPPFLAGS += -DBLE_UDP_ENABLE
endif
KBUILD_CPPFLAGS += -DBLE_FTMS_ENABLED
endif

## Fitness Machine Service collector
export BLE_FTMC ?= 0
ifeq ($(BLE_FTMC),1)
KBUILD_CPPFLAGS += -DBLE_FTMC_ENABLED
endif

export BES_AHP ?= 0
ifeq ($(BES_AHP),1)
KBUILD_CPPFLAGS += -DBES_AHP=1
KBUILD_CPPFLAGS += -DCFG_APP_AHP_SERVER
KBUILD_CPPFLAGS += -DCFG_APP_AHP_CLIENT
endif

## Heart Rate Sensor
export BLE_HRPS_ENABLE ?= 0
ifeq ($(BLE_HRPS_ENABLE),1)
KBUILD_CPPFLAGS += -DBLE_HRPS_ENABLED
endif

## Heart Rate Collector
export BLE_HRPC_ENABLE ?= 0
ifeq ($(BLE_HRPC_ENABLE),1)
KBUILD_CPPFLAGS += -DBLE_HRPC_ENABLED
endif

export BLE_CSCPS_ENABLE ?= 0
ifeq ($(BLE_CSCPS_ENABLE),1)
KBUILD_CPPFLAGS += -DBLE_CSCPS_ENABLED
endif

export BLE_CSCPC_ENABLE ?= 0
ifeq ($(BLE_CSCPC_ENABLE),1)
KBUILD_CPPFLAGS += -DBLE_CSCPC_ENABLED
endif

export BLE_PRF_TXP ?= 0
ifeq ($(BLE_PRF_TXP),1)
KBUILD_CPPFLAGS += -DBLE_TXP_ENABLED
endif

## old stack cfg
KBUILD_CPPFLAGS += -DBES_BLE_ACTIVITY_MAX=11
KBUILD_CPPFLAGS += -DBLE_STACK_PORTING_CHANGES

ifeq ($(CFG_LE_PWR_CTRL),1)
KBUILD_CPPFLAGS += -DCFG_LE_PWR_CTRL
endif

ifeq ($(GFPS_ENABLE),1)
KBUILD_CPPFLAGS += -DCFG_APP_SEC
endif

export USE_MS_AS_BLE_ADV_INTERVAL_UNIT ?= 1
ifeq ($(USE_MS_AS_BLE_ADV_INTERVAL_UNIT),1)
KBUILD_CPPFLAGS += -DUSE_MS_AS_BLE_ADV_INTERVAL_UNIT
endif

ifeq ($(AOB_MOBILE_ENABLED),1)
KBUILD_CPPFLAGS += -DBLE_AUDIO_IS_SUPPORT_CENTRAL_ROLE
endif

export IO_CAPABILITY_NO_INPUT_NO_OUTPUT_MITM_FALSE ?= 0
ifeq ($(IO_CAPABILITY_NO_INPUT_NO_OUTPUT_MITM_FALSE),1)
KBUILD_CPPFLAGS += -DIO_CAPABILITY_NO_INPUT_NO_OUTPUT_MITM_FALSE
endif

ifeq ($(CFG_APP_MESH),1)
KBUILD_CPPFLAGS += -DCFG_APP_MESH
endif # CFG_APP_MESH

ifeq ($(CFG_APP_HT),1)
KBUILD_CPPFLAGS += -DCFG_APP_HT
endif # CFG_APP_HT

ifeq ($(CFG_APP_HR),1)
KBUILD_CPPFLAGS += -DCFG_APP_HR
endif # CFG_APP_HR

ifeq ($(CFG_AHITL),1)
KBUILD_CPPFLAGS += -DCFG_AHITL
endif # Application Host Interface

ifeq ($(CFG_CON_CTE_REQ),1)
KBUILD_CPPFLAGS += -DCFG_CON_CTE_REQ
endif

ifeq ($(CFG_CON_CTE_RSP),1)
KBUILD_CPPFLAGS += -DCFG_CON_CTE_RSP
endif

ifeq ($(CFG_CONLESS_CTE_TX),1)
KBUILD_CPPFLAGS += -DCFG_CONLESS_CTE_TX
endif

ifeq ($(CFG_CONLESS_CTE_RX),1)
KBUILD_CPPFLAGS += -DCFG_CONLESS_CTE_RX
endif

ifeq ($(CFG_AOD),1)
KBUILD_CPPFLAGS += -DCFG_AOD
endif

export CFG_AOA ?= 0
ifeq ($(CFG_AOA),1)
KBUILD_CPPFLAGS += -DCFG_AOA
endif

export CFG_SUBRATE ?= 0
ifeq ($(CFG_SUBRATE),1)
KBUILD_CPPFLAGS += -DCFG_SUBRATE
endif


#----- BLE audio features -----#
ifeq ($(BLE_AUDIO_ENABLED),1)

export ISO_BEARER_SUPPORT ?= 1
ifeq ($(ISO_BEARER_SUPPORT),1)
KBUILD_CPPFLAGS += -DISO_BEARER_SUPPORT
endif

KBUILD_CPPFLAGS += -DBLE_AUDIO_IS_ALWAYS_USE_TEST_MODE_CIG_BIG_CREATING

export AOB_SPLIT_PAC_RECORD_INTO_FOUR_RECORDS ?= 0
ifeq ($(AOB_SPLIT_PAC_RECORD_INTO_FOUR_RECORDS),1)
KBUILD_CPPFLAGS += -DAOB_SPLIT_PAC_RECORD_INTO_FOUR_RECORDS
endif

export APP_BLE_BIS_DELEG_ENABLE ?= 1
export APP_BLE_BIS_SINK_ENABLE ?= 1
ifeq ($(AOB_MOBILE_ENABLED),1)
export APP_BLE_BIS_SRC_ENABLE ?= 1
export APP_BLE_BIS_ASSIST_ENABLE ?= 1
else
export APP_BLE_BIS_SRC_ENABLE ?= 0
export APP_BLE_BIS_ASSIST_ENABLE ?= 0
endif #AOB_MOBILE_ENABLED

export CFG_BAP_BC := 0
ifeq ($(APP_BLE_BIS_SRC_ENABLE),1)
KBUILD_CPPFLAGS += -DAPP_BLE_BIS_SRC_ENABLE
CFG_BAP_BC := 1
endif #APP_BLE_BIS_SRC_ENABLE

ifeq ($(APP_BLE_BIS_ASSIST_ENABLE),1)
KBUILD_CPPFLAGS += -DAPP_BLE_BIS_ASSIST_ENABLE
CFG_BAP_BC := 1
endif #APP_BLE_BIS_ASSIST_ENABLE

ifeq ($(APP_BLE_BIS_DELEG_ENABLE),1)
KBUILD_CPPFLAGS += -DAPP_BLE_BIS_DELEG_ENABLE
CFG_BAP_BC := 1
endif #APP_BLE_BIS_DELEG_ENABLE

ifeq ($(APP_BLE_BIS_SINK_ENABLE),1)
KBUILD_CPPFLAGS += -DAPP_BLE_BIS_SINK_ENABLE
CFG_BAP_BC := 1
endif #APP_BLE_BIS_SINK_ENABLE

ifeq ($(CFG_BAP_BC),1)
KBUILD_CPPFLAGS += -DCFG_BAP_BC
endif

export APP_BLE_DEMO_APP_ENABLED ?= 0
ifeq ($(APP_BLE_DEMO_APP_ENABLED),1)
KBUILD_CPPFLAGS += -DAPP_BLE_DEMO_APP_ENABLED
endif

EATT_CHAN_SUPPORT := 1
endif #BLE_AUDIO_ENABLED

#----- BLE extra features -----#
EATT_CHAN_SUPPORT ?= 0
ifeq ($(EATT_CHAN_SUPPORT), 1)
export EATT_CHAN_SUPPORT
KBUILD_CPPFLAGS += -DEATT_CHAN_SUPPORT
endif
endif #BLE

# -------------------------------------------
#  middleware features
# -------------------------------------------
export BTHOST_MIDDLEWARE ?= DISABLE

# -------------------------------------------
# nearlink features
# -------------------------------------------
export NEARLINK_HOST_SUPPORT ?= 0
ifeq ($(NEARLINK_HOST_SUPPORT),1)
KBUILD_CPPFLAGS += -DNEARLINK_HOST_SUPPORT
endif

export NEAR_DIP_SUPPORT ?= 0
ifeq ($(NEAR_DIP_SUPPORT),1)
KBUILD_CPPFLAGS += -DNEAR_DIP_SUPPORT
endif

export NEAR_BATT_SUPPORT ?= 0
ifeq ($(NEAR_BATT_SUPPORT),1)
KBUILD_CPPFLAGS += -DNEAR_BATT_SUPPORT
endif

export NEAR_SPP_SUPPORT ?= 0
ifeq ($(NEAR_SPP_SUPPORT),1)
KBUILD_CPPFLAGS += -DNEAR_SPP_SUPPORT
endif

export NEAR_HID_DEVICE_SUPPORT ?= 0
ifeq ($(NEAR_HID_DEVICE_SUPPORT),1)
KBUILD_CPPFLAGS += -DNEAR_HID_DEVICE_SUPPORT
endif

export NEAR_HID_HOST_SUPPORT ?= 0
ifeq ($(NEAR_HID_HOST_SUPPORT),1)
KBUILD_CPPFLAGS += -DNEAR_HID_HOST_SUPPORT
endif

NEAR_APP_DIR_PATH := bthost/service/near_app
NEAR_STACK_DIR_PATH := bthost/stack/nearlink

export NEAR_STACK_INCLUDES := \
    -I$(NEAR_APP_DIR_PATH)/inc \
    -I$(NEAR_STACK_DIR_PATH)/inc
