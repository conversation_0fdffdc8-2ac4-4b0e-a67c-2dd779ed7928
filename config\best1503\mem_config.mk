############ RAM OPTIMIZE ############
export USE_BASIC_THREADS ?= 0
export SCO_OPTIMIZE_FOR_RAM ?= 0
export BT_USE_COHEAP_ALLOC  ?= 1
export UNIFY_HEAP_ENABLED ?= 1
export FAST_RAM_OPTIMIZE ?= 0
export USE_OVERLAY_TXT_GAP ?= 0
########## RAM OPTIMIZE END ##########

ifeq ($(A2DP_CP_ACCEL),1)
export RAMCP_SIZE := 0x40000
export RAMCPX_SIZE := 0x20000
else
export RAMCP_SIZE := 0
export RAMCPX_SIZE := 0
endif

export FAST_XRAM_SECTION_SIZE ?= 0x10000

ifeq ($(USE_BASIC_THREADS),1)
ifeq ($(TOTA_v2),1)
BESBT_STACK_SIZE := 1024*4
else
ifeq ($(BLE_AUDIO_ENABLED),1)
BESBT_STACK_SIZE := 1024*3
else
BESBT_STACK_SIZE := 1024*2+512
endif
endif
KBUILD_CPPFLAGS += -DAF_STACK_SIZE=1024*3+512
ifeq ($(DIRAC_AUDIO_ENABLE),1)
KBUILD_CPPFLAGS += -DAPP_THREAD_STACK_SIZE=1024*3
else
KBUILD_CPPFLAGS += -DAPP_THREAD_STACK_SIZE=1024*2
endif
KBUILD_CPPFLAGS += -DINTERSYS_STACK_SIZE=1024
export OS_TIMER_THREAD_STACK_SIZE ?= 0x2000
endif
ifeq ($(A2DP_LHDC_ON),1)
export OS_DYNAMIC_MEM_SIZE ?= 0x12000
else
export OS_DYNAMIC_MEM_SIZE ?= 0x7000
endif
export CP_IN_CACHE_SIZE := 1024*3

KBUILD_CPPFLAGS += -DFAST_XRAM_SECTION_SIZE=$(FAST_XRAM_SECTION_SIZE)
KBUILD_CPPFLAGS += -DRAMCP_SIZE=$(RAMCP_SIZE)
KBUILD_CPPFLAGS += -DRAMCPX_SIZE=$(RAMCPX_SIZE)
KBUILD_CPPFLAGS += -DCP_IN_CACHE_SIZE=$(CP_IN_CACHE_SIZE)
