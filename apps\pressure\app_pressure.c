/***************************************************************************
 *
 * Copyright 2024-2024 BES.
 * All rights reserved. All unpublished rights reserved.
 *
 * No part of this work may be used or reproduced in any form or by any
 * means, or stored in a database or retrieval system, without prior written
 * permission of BES.
 *
 * Use of this work is governed by a license granted by BES.
 * This work contains confidential and proprietary information of
 * BES. which is protected by copyright, trade secret,
 * trademark and other intellectual property rights.
 *
 ****************************************************************************/

#include "app_pressure.h"
#include "pressure_core.h"

/***************************************************************************
 * @brief  app_mcu_core_pressure_init function
 *
 ***************************************************************************/
void app_mcu_core_pressure_init(void)
{
    pressure_core_thread_init();
}

