/***************************************************************************
 *
 * Copyright 2015-2025 BES.
 * All rights reserved. All unpublished rights reserved.
 *
 * No part of this work may be used or reproduced in any form or by any
 * means, or stored in a database or retrieval system, without prior written
 * permission of BES.
 *
 * Use of this work is governed by a license granted by BES.
 * This work contains confidential and proprietary information of
 * BES. which is protected by copyright, trade secret,
 * trademark and other intellectual property rights.
 *
 ****************************************************************************/
#ifdef BLE_WIFI_SRV_ENABLED
#include "gatt_service.h"
#include "ble_wifi_srv.h"
#include "app_ble.h"
#include "bes_dp_api.h"

#define BLE_WIFI_SRV_PREFERRED_MTU      512
#define BLE_WIFI_SRV_CONN_INTERVAL      (15) //ms

#define ble_wifi_service_uuid_128_content      0x12,0x34,0x56,0x78,0x90,0x00,0x00,0x80,0x00,0x10,0x00,0x00,0x00,0x04,0x00,0x04
#define ble_wifi_tx_char_val_uuid_128_content  0x12,0x34,0x56,0x78,0x91,0x00,0x00,0x80,0x00,0x10,0x00,0x00,0x00,0x05,0x00,0x05
#define ble_wifi_rx_char_val_uuid_128_content  0x12,0x34,0x56,0x78,0x92,0x00,0x00,0x80,0x00,0x10,0x00,0x00,0x00,0x06,0x00,0x06

GATT_DECL_128_LE_PRI_SERVICE(g_ble_wifi_service,
    ble_wifi_service_uuid_128_content);

GATT_DECL_128_LE_CHAR(g_ble_wifi_tx_character,
    ble_wifi_tx_char_val_uuid_128_content,
    GATT_NTF_PROP,
    ATT_SEC_NONE);

GATT_DECL_CCCD_DESCRIPTOR(g_ble_wifi_tx_cccd,
    ATT_SEC_NONE);

GATT_DECL_CUDD_DESCRIPTOR(g_ble_wifi_tx_cudd,
    ATT_SEC_NONE);

GATT_DECL_128_LE_CHAR(g_ble_wifi_rx_character,
    ble_wifi_rx_char_val_uuid_128_content,
    GATT_WR_REQ|GATT_WR_CMD|GATT_RD_REQ,
    ATT_SEC_NONE);

GATT_DECL_CUDD_DESCRIPTOR(g_ble_wifi_rx_cudd,
    ATT_SEC_NONE);

static const gatt_attribute_t g_ble_wifi_attr_list[] = {
    /* Service */
    gatt_attribute(g_ble_wifi_service),
    /* Characteristics */
    gatt_attribute(g_ble_wifi_tx_character),
        gatt_attribute(g_ble_wifi_tx_cccd),
        gatt_attribute(g_ble_wifi_tx_cudd),
    /* Characteristics */
    gatt_attribute(g_ble_wifi_rx_character),
        gatt_attribute(g_ble_wifi_rx_cudd),
};

static struct ble_wifi_server_env_tag ble_wifi_server_env = {0};
static ble_wifi_event_cb bw_event_callback = NULL;

uint8_t ble_wifi_srv_send_data_via_notification(uint8_t* data, uint32_t len)
{
    uint8_t conidx = ble_wifi_server_env.connectionIndex;
    if (BLE_INVALID_CONNECTION_INDEX == conidx || \
            !(ble_wifi_server_env.NtfIndCfg>>8))
    {
        return BT_STS_FAILED;
    }

    gatt_char_notify_t val_ntf=
    {
        .character = g_ble_wifi_tx_character,
        .service = g_ble_wifi_service,
    };

    return gatts_send_value_notification(gap_conn_bf(gap_zero_based_conidx_to_ble_conidx(conidx)), &val_ntf, data, len);
}

uint8_t ble_wifi_srv_send_data_via_indication(uint8_t* data, uint32_t len)
{
    uint8_t conidx = ble_wifi_server_env.connectionIndex;
    if (BLE_INVALID_CONNECTION_INDEX == conidx || \
            !(ble_wifi_server_env.NtfIndCfg&0xff))
    {
        return BT_STS_FAILED;
    }

    gatt_char_notify_t val_ind=
    {
        .character = g_ble_wifi_tx_character,
        .service = g_ble_wifi_service,
    };

    return gatts_send_value_indication(gap_conn_bf(gap_zero_based_conidx_to_ble_conidx(conidx)), &val_ind, data, len);
}

static void ble_wifi_srv_mtu_exchanged(uint8_t conidx, uint16_t connhdl, uint16_t mtu)
{
    return;
}

static void ble_wifi_srv_connected(uint8_t conidx, bool notify_enabled, \
                                                bool indicate_enabled, const uint8_t *descriptor)
{

    ble_wifi_server_env.connectionIndex = conidx;
    if (g_ble_wifi_tx_cccd == descriptor)
    {
        if (notify_enabled) {
            ble_wifi_server_env.NtfIndCfg |= notify_enabled<<8;
        } else if (indicate_enabled) {
            ble_wifi_server_env.NtfIndCfg |= indicate_enabled;
        }
    }

    if (bw_event_callback)
    {
        ble_wifi_param_u param = {
            .event = BLE_WIFI_SRV_CONN,
            .conidx = conidx,
            .data =    NULL,
            .len = 0,
        };
        bw_event_callback(&param);
    }
}

static void app_ble_wifi_server_disconnected(uint8_t conidx, const uint8_t *descriptor)
{
    if (NULL == descriptor)
    {
        memset(&ble_wifi_server_env, 0, sizeof(struct ble_wifi_server_env_tag));
        ble_wifi_server_env.connectionIndex = BLE_INVALID_CONNECTION_INDEX;
    }
    else if(g_ble_wifi_tx_cccd == descriptor)
    {
        ble_wifi_server_env.NtfIndCfg = 0;
    }

    if (bw_event_callback)
    {
        ble_wifi_param_u param = {
            .event = BLE_WIFI_SRV_DISCONN,
            .conidx = conidx,
            .data =    NULL,
            .len = 0,
        };
        bw_event_callback(&param);
    }
}

static void app_ble_wifi_server_tx_ccc_changed(uint8_t conidx, uint16_t config, const uint8_t *descriptor)
{
    if (config & GATT_CCCD_SET_NOTIFICATION)
    {
        ble_wifi_srv_connected(conidx, true, false, descriptor);
    }
    else if (config & GATT_CCCD_SET_INDICATION)
    {
        ble_wifi_srv_connected(conidx, false, true, descriptor);
    }
    else
    {
        app_ble_wifi_server_disconnected(conidx, descriptor);
    }
}

static void ble_wifi_srv_tx_data_sent(uint8_t conidx, const uint8_t *character)
{
    if (bw_event_callback)
    {
        ble_wifi_param_u param = {
            .event = BLE_WIFI_SRV_SENDDONE,
            .conidx = conidx,
            .data =    NULL,
            .len = 0,
        };
        bw_event_callback(&param);
    }
}

static att_error_code_t ble_wifi_srv_rx_data_received(uint8_t conidx, uint8_t *data, \
                                                                uint16_t len, const uint8_t *character)
{
    att_error_code_t status = ATT_ERROR_NO_ERROR;

    if (bw_event_callback)
    {
        ble_wifi_param_u param = {
            .event = BLE_WIFI_SRV_RX,
            .conidx = conidx,
            .data =    data,
            .len = len,
        };
        bw_event_callback(&param);
    }

    return status;
}

static bool ble_wifi_srv_callback(gatt_svc_t *svc, gatt_server_event_t event, gatt_server_callback_param_t param)
{
    switch (event)
    {
        case GATT_SERV_EVENT_CONN_OPENED:
            break;
        case GATT_SERV_EVENT_CONN_CLOSED:
        {
            app_ble_wifi_server_disconnected(gap_zero_based_conidx(svc->con_idx), NULL);
            break;
        }
        case GATT_SERV_EVENT_CONN_UPDATED:
            break;
        case GATT_SERV_EVENT_MTU_CHANGED:
        {
            gatt_server_mtu_changed_t *p = param.mtu_changed;
            ble_wifi_srv_mtu_exchanged(gap_zero_based_conidx(svc->con_idx), svc->connhdl, p->mtu);
            gap_update_params_t update_param = {0};
            update_param.conn_interval_min_1_25ms = (BLE_WIFI_SRV_CONN_INTERVAL * 100) / 125;
            update_param.conn_interval_max_1_25ms = (BLE_WIFI_SRV_CONN_INTERVAL * 100) / 125;
            update_param.max_peripheral_latency = 0;
            gap_update_le_conn_parameters(svc->connhdl, &update_param);
            break;
        }
        case GATT_SERV_EVENT_CHAR_WRITE:
        {
            gatt_server_char_write_t *p = param.char_write;
            if (p->value_offset != 0 || p->value_len == 0 || p->value == NULL)
            {
                return false;
            }
            p->rsp_error_code = ble_wifi_srv_rx_data_received(gap_zero_based_conidx(svc->con_idx), \
                                                                (uint8_t *)p->value, p->value_len, p->character);
            if (p->rsp_error_code == ATT_ERROR_NO_ERROR)
            {
                gatts_send_write_rsp(p->conn->connhdl, p->token, p->rsp_error_code);
            }
            return p->rsp_error_code;
        }
        case GATT_SERV_EVENT_DESC_WRITE:
        {
            gatt_server_desc_write_t *p = param.desc_write;
            uint16_t config = CO_COMBINE_UINT16_LE(p->value);
            app_ble_wifi_server_tx_ccc_changed(gap_zero_based_conidx(svc->con_idx), config, (uint8_t *)p->desc_attr->attr_data);
            return true;
        }
        case GATT_SERV_EVENT_NTF_TX_DONE:
        case GATT_SERV_EVENT_INDICATE_CFM:
        {
            gatt_server_indicate_cfm_t *p = param.confirm;
            ble_wifi_srv_tx_data_sent(gap_zero_based_conidx(svc->con_idx), p->character);
            break;
        }
        case GATT_SERV_EVENT_DESC_READ:
        {
            break;
        }
        default:
        {
            break;
        }
    }

    return 0;
}

void ble_wifi_srv_register_event_cb(ble_wifi_event_cb callback)
{
    bw_event_callback = callback;
}

void ble_wifi_srv_init(void)
{
    memset(&ble_wifi_server_env, 0, sizeof(struct ble_wifi_server_env_tag));
    ble_wifi_server_env.connectionIndex =  BLE_INVALID_CONNECTION_INDEX;

    gatts_cfg_t gatt_svc_cfg = {0};
    gatt_svc_cfg.preferred_mtu = BLE_WIFI_SRV_PREFERRED_MTU;
    gatt_svc_cfg.dont_delay_report_conn_open = true;

    gatts_register_service(g_ble_wifi_attr_list, \
                            ARRAY_SIZE(g_ble_wifi_attr_list), \
                            ble_wifi_srv_callback, &gatt_svc_cfg);
}

#endif /* BLE_WIFI_SRV_ENABLED */
